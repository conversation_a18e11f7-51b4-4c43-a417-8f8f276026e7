#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增量更新模块
实现数据的增量更新功能，避免重复下载历史数据
"""

import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from src.core.logger import LoggerMixin
from src.core.database import DatabaseManager
from src.storage.data_storage import DataStorage


class IncrementalUpdater(LoggerMixin):
    """增量更新器"""
    
    def __init__(self, db_manager: DatabaseManager, storage: DataStorage):
        super().__init__()
        self.db_manager = db_manager
        self.storage = storage
        
        # 定义需要增量更新的数据类型及其更新策略
        self.update_strategies = {
            'stock_basic': {
                'update_frequency': 'weekly',  # 每周更新
                'date_column': None,  # 无日期列，全量更新
                'incremental': False
            },
            'trade_cal': {
                'update_frequency': 'monthly',  # 每月更新
                'date_column': 'cal_date',
                'incremental': True
            },
            'daily': {
                'update_frequency': 'daily',  # 每日更新
                'date_column': 'trade_date',
                'incremental': True
            },
            'income': {
                'update_frequency': 'quarterly',  # 每季度更新
                'date_column': 'end_date',
                'incremental': True
            },
            'index_basic': {
                'update_frequency': 'monthly',  # 每月更新
                'date_column': None,
                'incremental': False
            },
            'index_daily': {
                'update_frequency': 'daily',  # 每日更新
                'date_column': 'trade_date',
                'incremental': True
            }
        }
    
    def check_update_needed(self, table_name: str) -> bool:
        """检查是否需要更新"""
        try:
            if table_name not in self.update_strategies:
                self.log_warning(f"表 {table_name} 没有定义更新策略")
                return False
            
            strategy = self.update_strategies[table_name]
            frequency = strategy['update_frequency']
            
            # 获取最后更新时间
            last_update = self._get_last_update_time(table_name)
            if not last_update:
                self.log_info(f"表 {table_name} 从未更新，需要更新")
                return True
            
            # 计算下次更新时间
            next_update = self._calculate_next_update_time(last_update, frequency)
            now = datetime.now()
            
            if now >= next_update:
                self.log_info(f"表 {table_name} 需要更新，上次更新: {last_update}, 下次更新: {next_update}")
                return True
            else:
                self.log_debug(f"表 {table_name} 暂不需要更新，下次更新时间: {next_update}")
                return False
                
        except Exception as e:
            self.log_error(f"检查表 {table_name} 更新需求失败", e)
            return False
    
    def get_incremental_date_range(self, table_name: str) -> Optional[tuple]:
        """获取增量更新的日期范围"""
        try:
            if table_name not in self.update_strategies:
                return None
            
            strategy = self.update_strategies[table_name]
            if not strategy['incremental'] or not strategy['date_column']:
                return None
            
            date_column = strategy['date_column']
            
            # 获取表中最新的日期
            latest_date = self._get_latest_date(table_name, date_column)
            if not latest_date:
                # 如果没有数据，返回默认范围
                end_date = datetime.now().strftime('%Y%m%d')
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
                return start_date, end_date
            
            # 从最新日期的下一天开始更新
            start_date = (datetime.strptime(latest_date, '%Y%m%d') + timedelta(days=1)).strftime('%Y%m%d')
            end_date = datetime.now().strftime('%Y%m%d')
            
            if start_date <= end_date:
                self.log_info(f"表 {table_name} 增量更新范围: {start_date} - {end_date}")
                return start_date, end_date
            else:
                self.log_debug(f"表 {table_name} 已是最新数据，无需增量更新")
                return None
                
        except Exception as e:
            self.log_error(f"获取表 {table_name} 增量更新范围失败", e)
            return None
    
    def update_last_update_time(self, table_name: str):
        """更新最后更新时间"""
        try:
            with self.db_manager.get_session() as session:
                # 检查是否已存在记录
                from sqlalchemy import text
                existing = session.execute(text("""
                    SELECT id FROM data_status 
                    WHERE table_name = :table_name AND date IS NULL
                """), {'table_name': table_name}).fetchone()
                
                now = datetime.now()
                
                if existing:
                    # 更新现有记录
                    session.execute(text("""
                        UPDATE data_status 
                        SET last_updated = :now
                        WHERE table_name = :table_name AND date IS NULL
                    """), {
                        'now': now,
                        'table_name': table_name
                    })
                else:
                    # 插入新记录
                    session.execute(text("""
                        INSERT INTO data_status 
                        (table_name, date, record_count, is_complete, last_updated, checksum)
                        VALUES (:table_name, NULL, 0, 1, :now, '')
                    """), {
                        'table_name': table_name,
                        'now': now
                    })
                
                session.commit()
                self.log_debug(f"更新表 {table_name} 的最后更新时间: {now}")
                
        except Exception as e:
            self.log_error(f"更新表 {table_name} 最后更新时间失败", e)
    
    def _get_last_update_time(self, table_name: str) -> Optional[datetime]:
        """获取最后更新时间"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text("""
                    SELECT last_updated FROM data_status 
                    WHERE table_name = :table_name AND date IS NULL
                    ORDER BY last_updated DESC LIMIT 1
                """), {'table_name': table_name}).fetchone()
                
                if result and result[0]:
                    # 确保返回datetime对象
                    if isinstance(result[0], str):
                        try:
                            if '.' in result[0]:
                                return datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S.%f')
                            else:
                                return datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            return datetime.now()
                    return result[0]
                return None
                
        except Exception as e:
            self.log_error(f"获取表 {table_name} 最后更新时间失败", e)
            return None
    
    def _get_latest_date(self, table_name: str, date_column: str) -> Optional[str]:
        """获取表中最新的日期"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text(f"""
                    SELECT MAX({date_column}) FROM {table_name}
                """)).fetchone()
                
                if result and result[0]:
                    return str(result[0])
                return None
                
        except Exception as e:
            self.log_error(f"获取表 {table_name} 最新日期失败", e)
            return None
    
    def _calculate_next_update_time(self, last_update: datetime, frequency: str) -> datetime:
        """计算下次更新时间"""
        if frequency == 'daily':
            return last_update + timedelta(days=1)
        elif frequency == 'weekly':
            return last_update + timedelta(weeks=1)
        elif frequency == 'monthly':
            return last_update + timedelta(days=30)
        elif frequency == 'quarterly':
            return last_update + timedelta(days=90)
        else:
            return last_update + timedelta(days=1)
    
    def get_tables_need_update(self) -> List[str]:
        """获取需要更新的表列表"""
        tables_to_update = []
        for table_name in self.update_strategies.keys():
            if self.check_update_needed(table_name):
                tables_to_update.append(table_name)
        
        self.log_info(f"需要更新的表: {tables_to_update}")
        return tables_to_update
