#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tushare数据下载系统主程序
支持断点续传、去重、完整性保证等功能
"""

import os
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.logger import setup_logger
from src.core.database import DatabaseManager
from src.downloader.data_downloader import DataDownloader
from src.utils.progress_tracker import ProgressTracker


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='tushare数据下载系统')
    parser.add_argument('--config', '-c', default='config.yaml', 
                       help='配置文件路径')
    parser.add_argument('--data-type', '-t', 
                       choices=['all', 'basic', 'daily', 'financial', 'index'],
                       default='all', help='下载数据类型')
    parser.add_argument('--start-date', '-s', 
                       help='开始日期 (YYYYMMDD)')
    parser.add_argument('--end-date', '-e', 
                       help='结束日期 (YYYYMMDD)')
    parser.add_argument('--resume', '-r', action='store_true',
                       help='从上次中断处继续下载')
    parser.add_argument('--validate', '-v', action='store_true',
                       help='验证数据完整性')
    parser.add_argument('--repair', action='store_true',
                       help='修复缺失或损坏的数据')
    
    args = parser.parse_args()
    
    try:
        # 初始化配置管理器
        config = ConfigManager(args.config)
        
        # 设置日志
        logger = setup_logger(config)
        logger.info("=== tushare数据下载系统启动 ===")
        
        # 初始化数据库
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        # 初始化进度跟踪器
        progress_tracker = ProgressTracker(db_manager)
        
        # 初始化数据下载器
        downloader = DataDownloader(config, db_manager, progress_tracker)
        
        if args.validate:
            logger.info("开始数据验证...")
            downloader.validate_data()
        elif args.repair:
            logger.info("开始数据修复...")
            downloader.repair_data()
        else:
            logger.info(f"开始下载数据类型: {args.data_type}")
            downloader.download_data(
                data_type=args.data_type,
                start_date=args.start_date,
                end_date=args.end_date,
                resume=args.resume
            )
        
        logger.info("=== 任务完成 ===")
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"程序执行出错: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
