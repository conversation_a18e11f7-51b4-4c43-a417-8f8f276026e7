#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
演示高级功能
展示增量更新、数据验证、监控等功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.database import DatabaseManager
from src.storage.data_storage import DataStorage
from src.utils.performance_optimizer import PerformanceOptimizer, PerformanceConfig


def demo_performance_optimizer():
    """演示性能优化器"""
    print("=== 性能优化器演示 ===")
    
    # 创建性能配置
    perf_config = PerformanceConfig(
        max_workers=2,
        batch_size=50,
        retry_attempts=2,
        rate_limit_calls=100,
        cache_enabled=True
    )
    
    # 创建性能优化器
    optimizer = PerformanceOptimizer(perf_config)
    
    # 模拟一些任务
    def mock_task(task_id, delay=0.1):
        """模拟任务"""
        import time
        time.sleep(delay)
        return f"Task {task_id} completed"
    
    # 创建任务列表
    tasks = [{'task_id': i, 'delay': 0.05} for i in range(10)]
    
    print(f"执行 {len(tasks)} 个模拟任务...")
    
    # 使用优化器执行任务
    results = optimizer.execute_with_optimization(mock_task, tasks, use_cache=False)
    
    print(f"完成 {len([r for r in results if r])} 个任务")
    
    # 获取性能报告
    report = optimizer.get_performance_report()
    print(f"性能报告:")
    print(f"  总请求数: {report['total_requests']}")
    print(f"  成功率: {report['success_rate']:.1f}%")
    print(f"  平均响应时间: {report['average_response_time']:.3f}秒")
    print(f"  请求速率: {report['requests_per_second']:.1f} req/s")
    
    print("✅ 性能优化器演示完成")


def demo_database_status():
    """演示数据库状态检查"""
    print("\n=== 数据库状态检查 ===")
    
    try:
        config = ConfigManager()
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        # 检查数据库连接
        with db_manager.get_session() as session:
            from sqlalchemy import text
            
            # 获取表列表
            tables = session.execute(text("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)).fetchall()
            
            print(f"数据库表数量: {len(tables)}")
            print("表列表:")
            for table in tables:
                print(f"  - {table[0]}")
            
            # 获取数据统计
            print("\n数据统计:")
            for table in tables:
                table_name = table[0]
                try:
                    count = session.execute(text(f"SELECT COUNT(*) FROM {table_name}")).scalar()
                    print(f"  {table_name}: {count:,} 条记录")
                except Exception as e:
                    print(f"  {table_name}: 查询失败 ({e})")
        
        print("✅ 数据库状态检查完成")
        
    except Exception as e:
        print(f"❌ 数据库状态检查失败: {e}")


def demo_config_management():
    """演示配置管理"""
    print("\n=== 配置管理演示 ===")
    
    try:
        config = ConfigManager()
        
        print("当前配置:")
        print(f"  数据库类型: {config.get('database.type')}")
        print(f"  数据库路径: {config.get('database.sqlite.path')}")
        print(f"  日志级别: {config.get('logging.level')}")
        print(f"  API限制: {config.get('api.rate_limit')} 次/分钟")
        print(f"  下载目录: {config.get('download.data_dir')}")
        
        # 检查tushare token
        token = config.get('tushare.token')
        if token and len(token) > 10:
            print(f"  Tushare Token: {token[:10]}...{token[-10:]} (已配置)")
        else:
            print(f"  Tushare Token: 未配置或无效")
        
        print("✅ 配置管理演示完成")
        
    except Exception as e:
        print(f"❌ 配置管理演示失败: {e}")


def main():
    """主函数"""
    print("🚀 tushare数据下载系统 - 高级功能演示")
    print("=" * 50)
    
    # 演示各个功能模块
    demo_config_management()
    demo_database_status()
    demo_performance_optimizer()
    
    print("\n" + "=" * 50)
    print("🎉 高级功能演示完成！")
    print("\n📋 可用的高级管理命令:")
    print("  python advanced_manager.py --help              # 查看所有选项")
    print("  python advanced_manager.py --check-updates     # 检查增量更新")
    print("  python advanced_manager.py --validate-all      # 验证所有数据")
    print("  python advanced_manager.py --status            # 显示系统状态")
    print("  python advanced_manager.py --performance       # 显示性能指标")
    print("  python advanced_manager.py --report daily      # 生成日报")
    print("  python advanced_manager.py --repair TABLE_NAME # 修复指定表")


if __name__ == '__main__':
    main()
