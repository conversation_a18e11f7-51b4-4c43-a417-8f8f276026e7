<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>tushare数据查看器 - 手机版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
            font-size: 16px;
        }
        
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 20px 15px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 1.8em;
            margin-bottom: 8px;
        }
        
        .header p {
            font-size: 1em;
            opacity: 0.9;
        }
        
        .summary-section {
            padding: 20px 15px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .summary-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .summary-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #2196F3;
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .progress-bar {
            width: 100%;
            height: 15px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            border-radius: 8px;
            transition: width 0.3s ease;
        }
        
        .tables-section {
            padding: 20px 15px;
        }
        
        .section-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .table-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }
        
        .table-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .table-item:active {
            transform: scale(0.98);
        }
        
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .table-name {
            font-size: 1.1em;
            font-weight: bold;
            color: #333;
        }
        
        .table-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-downloaded {
            background: #4CAF50;
            color: white;
        }
        
        .status-empty {
            background: #f44336;
            color: white;
        }
        
        .table-chinese {
            color: #2196F3;
            font-size: 1em;
            margin-bottom: 5px;
        }
        
        .table-count {
            color: #666;
            font-size: 0.9em;
        }
        
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin: 15px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .refresh-btn:active {
            transform: scale(0.98);
        }
        
        .loading {
            text-align: center;
            padding: 30px;
            color: #666;
            font-size: 1.1em;
        }
        
        .footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 0.9em;
            background: #f8f9fa;
        }
        
        /* 新增：手机版操作按钮样式 */
        .mobile-actions {
            display: flex;
            gap: 10px;
            margin: 15px 0;
        }

        .action-btn {
            flex: 1;
            padding: 12px 8px;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .action-btn.update-all {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
        }

        .action-btn.download-missing {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
        }

        .action-btn:active {
            transform: scale(0.95);
        }

        .download-status {
            background: #f0f8ff;
            border: 1px solid #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-header h4 {
            color: #1976d2;
            margin: 0;
            font-size: 16px;
        }

        .cancel-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .status-progress {
            margin-bottom: 10px;
        }

        .status-bar {
            width: 100%;
            height: 15px;
            background: #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .status-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            border-radius: 8px;
            transition: width 0.3s ease;
        }

        #statusText {
            color: #666;
            font-size: 12px;
        }

        /* 表项操作按钮 */
        .table-item-actions {
            margin-top: 10px;
            display: flex;
            gap: 5px;
        }

        .table-action-btn {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f5f5f5;
            color: #333;
            font-size: 11px;
            cursor: pointer;
        }

        .table-action-btn:active {
            background: #e0e0e0;
        }

        /* 深色模式适配 */
        @media (prefers-color-scheme: dark) {
            body {
                background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            }

            .container {
                background: #2c3e50;
                color: white;
            }

            .summary-item, .table-item {
                background: #34495e;
                border-color: #4a5f7a;
            }

            .table-name {
                color: white;
            }

            .summary-section, .footer {
                background: #34495e;
            }

            .download-status {
                background: #34495e;
                border-color: #4a5f7a;
            }

            .table-action-btn {
                background: #4a5f7a;
                border-color: #5a6f8a;
                color: white;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 tushare数据查看器</h1>
            <p>手机版 • 随时查看下载进度</p>
        </div>
        
        <div class="summary-section">
            <div class="summary-grid" id="summaryGrid">
                <div class="summary-item">
                    <div class="summary-number" id="totalTables">-</div>
                    <div class="summary-label">总表数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="downloadedTables">-</div>
                    <div class="summary-label">已下载</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="totalRecords">-</div>
                    <div class="summary-label">总记录数</div>
                </div>
                <div class="summary-item">
                    <div class="summary-number" id="completionRate">-</div>
                    <div class="summary-label">完成率</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            
            <button class="refresh-btn" onclick="loadData()">🔄 刷新数据</button>

            <!-- 新增：操作按钮 -->
            <div class="mobile-actions">
                <button class="action-btn update-all" onclick="updateAllTables()">
                    🔄 更新所有数据
                </button>
                <button class="action-btn download-missing" onclick="downloadMissingTables()">
                    📥 下载缺失数据
                </button>
            </div>

            <!-- 新增：下载状态显示 -->
            <div id="downloadStatus" class="download-status" style="display: none;">
                <div class="status-header">
                    <h4 id="statusTitle">下载状态</h4>
                    <button class="cancel-btn" onclick="cancelCurrentDownload()" id="cancelBtn" style="display: none;">❌</button>
                </div>
                <div class="status-progress">
                    <div class="status-bar">
                        <div class="status-fill" id="statusFill" style="width: 0%"></div>
                    </div>
                    <div id="statusText">准备中...</div>
                </div>
            </div>
        </div>
        
        <div class="tables-section">
            <div class="section-title">📋 数据表列表</div>
            
            <div class="table-list" id="tableList">
                <div class="loading">正在加载数据...</div>
            </div>
        </div>
        
        <div class="footer">
            <p>📱 tushare数据管理中心 手机版</p>
            <p>实时同步 • 随时查看</p>
        </div>
    </div>

    <script>
        // 页面加载时初始化
        window.onload = function() {
            loadData();
            
            // 每30秒自动刷新
            setInterval(loadData, 30000);
        };

        // 加载所有数据
        function loadData() {
            loadSummary();
            loadTables();
        }

        // 加载汇总信息
        function loadSummary() {
            fetch('/api/mobile/summary')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('totalTables').textContent = data.total_tables;
                    document.getElementById('downloadedTables').textContent = data.downloaded_tables;
                    document.getElementById('totalRecords').textContent = data.total_records.toLocaleString();
                    document.getElementById('completionRate').textContent = data.completion_rate + '%';
                    document.getElementById('progressFill').style.width = data.completion_rate + '%';
                })
                .catch(error => {
                    console.error('加载汇总信息失败:', error);
                });
        }

        // 加载表列表
        function loadTables() {
            fetch('/api/mobile/tables/enhanced')
                .then(response => response.json())
                .then(data => {
                    const tableList = document.getElementById('tableList');
                    
                    if (data.length === 0) {
                        tableList.innerHTML = '<div class="loading">暂无数据表</div>';
                        return;
                    }
                    
                    let html = '';
                    data.forEach(table => {
                        const statusClass = 'status-' + (table.status || 'unknown');
                        const countDisplay = table.count > 0 ? table.count.toLocaleString() + ' 条记录' : '暂无数据';

                        // 生成操作按钮
                        const actionButtons = `
                            <div class="table-item-actions">
                                <button class="table-action-btn" onclick="downloadSingleTable('${table.name}', 'full')">
                                    📥 重新下载
                                </button>
                                <button class="table-action-btn" onclick="downloadSingleTable('${table.name}', 'incremental')">
                                    🔄 增量更新
                                </button>
                            </div>
                        `;

                        html += `
                            <div class="table-item">
                                <div class="table-header">
                                    <div class="table-name">${table.name}</div>
                                    <div class="table-status ${statusClass}">${table.status_text || table.status}</div>
                                </div>
                                <div class="table-chinese">${table.chinese_name}</div>
                                <div class="table-count">${countDisplay}</div>
                                ${actionButtons}
                            </div>
                        `;
                    });
                    
                    tableList.innerHTML = html;
                })
                .catch(error => {
                    console.error('加载表列表失败:', error);
                    document.getElementById('tableList').innerHTML = '<div class="loading">加载失败</div>';
                });
        }

        // 添加触摸反馈
        document.addEventListener('touchstart', function() {}, {passive: true});

        // ==================== 新增下载管理功能 ====================

        let currentTaskId = null;
        let statusCheckInterval = null;

        // 下载单个表
        function downloadSingleTable(tableName, mode) {
            const modeText = mode === 'full' ? '重新下载' : '增量更新';
            const confirmMessage = `确定要${modeText} "${tableName}" 表吗？`;

            if (!confirm(confirmMessage)) {
                return;
            }

            fetch('/api/mobile/download/single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    table_name: tableName,
                    mode: mode
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }

        // 批量更新所有数据
        function updateAllTables() {
            if (!confirm('确定要更新所有已下载的数据表吗？')) {
                return;
            }

            fetch('/api/mobile/download/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation: 'update_all'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }

        // 下载所有缺失数据
        function downloadMissingTables() {
            if (!confirm('确定要下载所有缺失的数据表吗？')) {
                return;
            }

            fetch('/api/mobile/download/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation: 'download_missing'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }

        // 显示下载状态
        function showDownloadStatus(message) {
            const statusDiv = document.getElementById('downloadStatus');
            const statusText = document.getElementById('statusText');
            const statusFill = document.getElementById('statusFill');
            const cancelBtn = document.getElementById('cancelBtn');

            statusDiv.style.display = 'block';
            statusText.textContent = message;
            statusFill.style.width = '0%';
            cancelBtn.style.display = 'inline-block';
        }

        // 隐藏下载状态
        function hideDownloadStatus() {
            const statusDiv = document.getElementById('downloadStatus');
            statusDiv.style.display = 'none';
            currentTaskId = null;

            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }

        // 开始状态监控
        function startStatusMonitoring() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(checkDownloadStatus, 3000); // 每3秒检查一次
        }

        // 检查下载状态
        function checkDownloadStatus() {
            if (!currentTaskId) {
                return;
            }

            fetch(`/api/mobile/download/status?task_id=${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.status) {
                        const status = data.status;
                        const statusText = document.getElementById('statusText');
                        const statusFill = document.getElementById('statusFill');

                        statusText.textContent = status.message;
                        statusFill.style.width = status.progress + '%';

                        if (status.status === 'completed') {
                            setTimeout(() => {
                                hideDownloadStatus();
                                loadData(); // 刷新所有数据
                                alert('操作完成！');
                            }, 2000);
                        } else if (status.status === 'failed') {
                            setTimeout(() => {
                                hideDownloadStatus();
                                alert('操作失败: ' + status.message);
                            }, 2000);
                        } else if (status.status === 'cancelled') {
                            hideDownloadStatus();
                            alert('操作已取消');
                        }
                    }
                })
                .catch(error => {
                    console.error('Status check error:', error);
                });
        }

        // 取消当前下载
        function cancelCurrentDownload() {
            if (!currentTaskId) {
                return;
            }

            if (!confirm('确定要取消当前操作吗？')) {
                return;
            }

            // 简单的取消操作，直接隐藏状态
            hideDownloadStatus();
            alert('操作已取消');
        }
    </script>
</body>
</html>
