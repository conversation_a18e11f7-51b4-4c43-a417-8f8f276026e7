#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责加载和管理系统配置
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from loguru import logger


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化配置管理器"""
        self.config_path = Path(config_path)
        self.config = {}
        self._load_config()
        self._validate_config()
    
    def _load_config(self):
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            
            # 从环境变量覆盖敏感配置
            self._load_env_overrides()
            
            logger.info(f"配置文件加载成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            raise
    
    def _load_env_overrides(self):
        """从环境变量加载配置覆盖"""
        # tushare token
        env_token = os.getenv('TUSHARE_TOKEN')
        if env_token:
            if 'tushare' not in self.config:
                self.config['tushare'] = {}
            self.config['tushare']['token'] = env_token
            logger.info("使用环境变量中的tushare token")
        
        # 数据库配置
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            # 解析数据库URL并更新配置
            self._parse_database_url(db_url)
    
    def _parse_database_url(self, db_url: str):
        """解析数据库URL"""
        # 简单的URL解析，支持postgresql://和sqlite://
        if db_url.startswith('postgresql://'):
            # 解析PostgreSQL URL
            # postgresql://username:password@host:port/database
            import urllib.parse as urlparse
            parsed = urlparse.urlparse(db_url)
            
            if 'database' not in self.config:
                self.config['database'] = {}
            
            self.config['database']['type'] = 'postgresql'
            self.config['database']['postgresql'] = {
                'host': parsed.hostname,
                'port': parsed.port or 5432,
                'database': parsed.path.lstrip('/'),
                'username': parsed.username,
                'password': parsed.password,
            }
            
        elif db_url.startswith('sqlite://'):
            # 解析SQLite URL
            db_path = db_url.replace('sqlite://', '')
            if 'database' not in self.config:
                self.config['database'] = {}
            
            self.config['database']['type'] = 'sqlite'
            self.config['database']['sqlite'] = {'path': db_path}
    
    def _validate_config(self):
        """验证配置的有效性"""
        # 检查必需的配置项
        required_configs = [
            ('tushare', 'token'),
        ]
        
        for config_path in required_configs:
            if not self._get_nested_config(config_path):
                raise ValueError(f"缺少必需的配置项: {'.'.join(config_path)}")
        
        # 验证tushare token格式
        token = self.get('tushare.token')
        if not token or len(token) != 64:
            logger.warning("tushare token格式可能不正确")
        
        # 验证数据库配置
        db_type = self.get('database.type', 'sqlite')
        if db_type not in ['sqlite', 'postgresql']:
            raise ValueError(f"不支持的数据库类型: {db_type}")
        
        logger.info("配置验证通过")
    
    def _get_nested_config(self, config_path: tuple) -> Any:
        """获取嵌套配置值"""
        value = self.config
        for key in config_path:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        return value
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def save(self, path: Optional[str] = None):
        """保存配置到文件"""
        save_path = Path(path) if path else self.config_path
        
        try:
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"配置已保存到: {save_path}")
            
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
            raise
    
    def get_tushare_config(self) -> Dict[str, Any]:
        """获取tushare配置"""
        return self.config.get('tushare', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.config.get('database', {})
    
    def get_download_config(self) -> Dict[str, Any]:
        """获取下载配置"""
        return self.config.get('download', {})
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        return self.config.get('storage', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.config.get('logging', {})
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return self.config.get('monitoring', {})
    
    def get_validation_config(self) -> Dict[str, Any]:
        """获取验证配置"""
        return self.config.get('validation', {})
    
    def __getitem__(self, key: str) -> Any:
        """支持字典式访问"""
        return self.get(key)
    
    def __setitem__(self, key: str, value: Any):
        """支持字典式设置"""
        self.set(key, value)
    
    def __contains__(self, key: str) -> bool:
        """支持in操作符"""
        return self.get(key) is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """返回配置字典的副本"""
        return self.config.copy()
