#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指数数据下载器
负责下载指数基本信息、日线数据等
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from ..core.logger import LoggerMixin


class IndexDataDownloader(LoggerMixin):
    """指数数据下载器"""
    
    def __init__(self, api, storage, progress_tracker, config):
        """初始化指数数据下载器"""
        self.api = api
        self.storage = storage
        self.progress_tracker = progress_tracker
        self.config = config
        
        # 获取下载配置
        self.download_config = config.get_download_config()
        self.batch_size = self.download_config.get('batch_size', 1000)
        
        self.log_info("指数数据下载器初始化完成")
    
    def download_index_basic(self):
        """下载指数基本信息"""
        try:
            self.log_info("开始下载指数基本信息")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='index',
                table_name='index_basic'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 下载数据
            data = self.api.get_index_basic()
            
            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('index_basic', data, if_exists='replace')
                
                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')
                
                self.log_info(f"指数基本信息下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("指数基本信息数据为空")
            
        except Exception as e:
            self.log_error("下载指数基本信息失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_index_daily(self, index_list: List[str], 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None):
        """下载指数日线数据"""
        try:
            self.log_info("开始下载指数日线数据")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='index',
                table_name='index_daily',
                start_date=start_date,
                end_date=end_date,
                total_records=len(index_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(index_list), "下载指数日线")
            
            total_records = 0
            
            # 按指数代码下载
            for i, ts_code in enumerate(index_list):
                try:
                    self.log_debug(f"下载指数 {ts_code} 日线数据")
                    
                    data = self.api.get_index_daily(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('index_daily', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('index_daily', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载指数 {ts_code} 日线数据失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"指数日线数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载指数日线数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_index_daily_basic(self, index_list: List[str], 
                                  start_date: Optional[str] = None,
                                  end_date: Optional[str] = None):
        """下载指数每日指标"""
        try:
            self.log_info("开始下载指数每日指标")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 获取交易日期列表
            trade_dates = self.storage.get_trade_dates(start_date, end_date)
            if not trade_dates:
                self.log_warning("未找到交易日期，跳过指数每日指标下载")
                return
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='index',
                table_name='index_dailybasic',
                start_date=start_date,
                end_date=end_date,
                total_records=len(trade_dates)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(trade_dates), "下载指数每日指标")
            
            total_records = 0
            
            # 按交易日期下载
            for i, trade_date in enumerate(trade_dates):
                try:
                    self.log_debug(f"下载 {trade_date} 指数每日指标")
                    
                    data = self.api.get_index_dailybasic(trade_date=trade_date)
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('index_dailybasic', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('index_dailybasic', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1, trade_date)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.2)
                    
                except Exception as e:
                    self.log_warning(f"下载 {trade_date} 指数每日指标失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"指数每日指标下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载指数每日指标失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_index_weight(self, index_list: List[str], 
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None):
        """下载指数成分和权重"""
        try:
            self.log_info("开始下载指数成分和权重")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='index',
                table_name='index_weight',
                start_date=start_date,
                end_date=end_date,
                total_records=len(index_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(index_list), "下载指数权重")
            
            total_records = 0
            
            # 按指数代码下载
            for i, index_code in enumerate(index_list):
                try:
                    self.log_debug(f"下载指数 {index_code} 成分权重")
                    
                    data = self.api.get_index_weight(
                        index_code=index_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('index_weight', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('index_weight', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载指数 {index_code} 成分权重失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"指数成分权重下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载指数成分权重失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def validate_data(self):
        """验证指数数据完整性"""
        try:
            self.log_info("开始验证指数数据完整性")
            
            # 验证指数基本信息
            index_basic_count = self.storage.get_data_count('index_basic')
            self.log_info(f"指数基本信息: {index_basic_count} 条记录")
            
            # 验证指数日线数据
            index_daily_count = self.storage.get_data_count('index_daily')
            self.log_info(f"指数日线数据: {index_daily_count} 条记录")
            
            # 验证指数每日指标
            index_dailybasic_count = self.storage.get_data_count('index_dailybasic')
            self.log_info(f"指数每日指标: {index_dailybasic_count} 条记录")
            
            self.log_info("指数数据验证完成")
            
        except Exception as e:
            self.log_error("验证指数数据失败", e)
            raise
    
    def repair_data(self):
        """修复指数数据"""
        try:
            self.log_info("开始修复指数数据")
            
            # 检查并修复指数基本信息
            if self.storage.get_data_count('index_basic') == 0:
                self.log_info("重新下载指数基本信息")
                self.download_index_basic()
            
            # 获取指数列表
            index_list = self.storage.get_index_list()
            if not index_list:
                self.log_warning("未找到指数列表，无法修复指数数据")
                return
            
            # 检查并修复指数日线数据
            if self.storage.get_data_count('index_daily') == 0:
                self.log_info("重新下载指数日线数据")
                self.download_index_daily(index_list)
            
            self.log_info("指数数据修复完成")
            
        except Exception as e:
            self.log_error("修复指数数据失败", e)
            raise
