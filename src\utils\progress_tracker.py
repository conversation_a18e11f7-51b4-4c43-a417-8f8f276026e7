#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度跟踪模块
负责记录和管理下载进度，支持断点续传
"""

import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import text
from tqdm import tqdm

from ..core.logger import LoggerMixin


class ProgressTracker(LoggerMixin):
    """进度跟踪器"""
    
    def __init__(self, db_manager):
        """初始化进度跟踪器"""
        self.db_manager = db_manager
        self.current_progress = {}
        self.progress_bars = {}
    
    def create_task(self, data_type: str, table_name: str, 
                   start_date: Optional[str] = None, 
                   end_date: Optional[str] = None,
                   total_records: int = 0) -> int:
        """创建下载任务"""
        try:
            with self.db_manager.get_session() as session:
                # 检查是否已存在相同任务
                existing_task = session.execute(text("""
                    SELECT id FROM download_progress 
                    WHERE data_type = :data_type 
                    AND table_name = :table_name 
                    AND start_date = :start_date 
                    AND end_date = :end_date
                    AND status != 'completed'
                """), {
                    'data_type': data_type,
                    'table_name': table_name,
                    'start_date': start_date,
                    'end_date': end_date
                }).fetchone()
                
                if existing_task:
                    task_id = existing_task[0]
                    self.log_info(f"找到未完成的任务: {task_id}")
                    return task_id
                
                # 创建新任务
                result = session.execute(text("""
                    INSERT INTO download_progress
                    (data_type, table_name, start_date, end_date, total_records,
                     downloaded_records, status, created_at, updated_at)
                    VALUES (:data_type, :table_name, :start_date, :end_date,
                            :total_records, 0, 'pending', :now, :now)
                """), {
                    'data_type': data_type,
                    'table_name': table_name,
                    'start_date': start_date,
                    'end_date': end_date,
                    'total_records': total_records,
                    'now': datetime.now()
                })

                # 获取插入的任务ID
                task_id = result.lastrowid
                session.commit()

                if task_id is None:
                    raise Exception("创建任务失败，无法获取任务ID")
                
                self.log_info(f"创建下载任务: {task_id} - {data_type}.{table_name}")
                return task_id
                
        except Exception as e:
            self.log_error(f"创建下载任务失败", e)
            raise
    
    def update_task_status(self, task_id: int, status: str, 
                          error_message: Optional[str] = None):
        """更新任务状态"""
        try:
            with self.db_manager.get_session() as session:
                update_data = {
                    'task_id': task_id,
                    'status': status,
                    'updated_at': datetime.now()
                }
                
                if error_message:
                    update_data['error_message'] = error_message
                
                if status == 'completed':
                    update_data['completed_at'] = datetime.now()
                
                session.execute(text("""
                    UPDATE download_progress 
                    SET status = :status, updated_at = :updated_at
                    """ + (", error_message = :error_message" if error_message else "") + 
                    (", completed_at = :completed_at" if status == 'completed' else "") + """
                    WHERE id = :task_id
                """), update_data)
                
                session.commit()
                
                self.log_progress(f"任务 {task_id} 状态更新为: {status}")
                
        except Exception as e:
            self.log_error(f"更新任务状态失败", e)
            raise
    
    def update_progress(self, task_id: int, downloaded_records: int, 
                       current_date: Optional[str] = None):
        """更新下载进度"""
        try:
            with self.db_manager.get_session() as session:
                update_data = {
                    'task_id': task_id,
                    'downloaded_records': downloaded_records,
                    'updated_at': datetime.now()
                }
                
                if current_date:
                    update_data['current_date'] = current_date
                
                session.execute(text("""
                    UPDATE download_progress 
                    SET downloaded_records = :downloaded_records, 
                        updated_at = :updated_at
                    """ + (", current_date = :current_date" if current_date else "") + """
                    WHERE id = :task_id
                """), update_data)
                
                session.commit()
                
                # 更新进度条
                if task_id in self.progress_bars:
                    self.progress_bars[task_id].update(downloaded_records - self.progress_bars[task_id].n)
                
        except Exception as e:
            self.log_error(f"更新下载进度失败", e)
            raise
    
    def get_task_progress(self, task_id: int) -> Optional[Dict[str, Any]]:
        """获取任务进度"""
        try:
            with self.db_manager.get_session() as session:
                result = session.execute(text("""
                    SELECT * FROM download_progress WHERE id = :task_id
                """), {'task_id': task_id}).fetchone()
                
                if result:
                    return {
                        'id': result[0],
                        'data_type': result[1],
                        'table_name': result[2],
                        'start_date': result[3],
                        'end_date': result[4],
                        'current_date': result[5],
                        'total_records': result[6],
                        'downloaded_records': result[7],
                        'status': result[8],
                        'error_message': result[9],
                        'created_at': result[10],
                        'updated_at': result[11],
                        'completed_at': result[12],
                    }
                
                return None
                
        except Exception as e:
            self.log_error(f"获取任务进度失败", e)
            return None
    
    def get_incomplete_tasks(self, data_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取未完成的任务"""
        try:
            with self.db_manager.get_session() as session:
                sql = """
                    SELECT * FROM download_progress 
                    WHERE status IN ('pending', 'running', 'failed')
                """
                params = {}
                
                if data_type:
                    sql += " AND data_type = :data_type"
                    params['data_type'] = data_type
                
                sql += " ORDER BY created_at"
                
                results = session.execute(text(sql), params).fetchall()
                
                tasks = []
                for result in results:
                    tasks.append({
                        'id': result[0],
                        'data_type': result[1],
                        'table_name': result[2],
                        'start_date': result[3],
                        'end_date': result[4],
                        'current_date': result[5],
                        'total_records': result[6],
                        'downloaded_records': result[7],
                        'status': result[8],
                        'error_message': result[9],
                        'created_at': result[10],
                        'updated_at': result[11],
                        'completed_at': result[12],
                    })
                
                return tasks
                
        except Exception as e:
            self.log_error(f"获取未完成任务失败", e)
            return []
    
    def create_progress_bar(self, task_id: int, total: int, description: str = ""):
        """创建进度条"""
        if task_id not in self.progress_bars:
            self.progress_bars[task_id] = tqdm(
                total=total,
                desc=description,
                unit="records",
                unit_scale=True,
                ncols=100
            )
    
    def close_progress_bar(self, task_id: int):
        """关闭进度条"""
        if task_id in self.progress_bars:
            self.progress_bars[task_id].close()
            del self.progress_bars[task_id]
    
    def get_download_statistics(self) -> Dict[str, Any]:
        """获取下载统计信息"""
        try:
            with self.db_manager.get_session() as session:
                # 总任务数
                total_tasks = session.execute(text("""
                    SELECT COUNT(*) FROM download_progress
                """)).fetchone()[0]
                
                # 已完成任务数
                completed_tasks = session.execute(text("""
                    SELECT COUNT(*) FROM download_progress WHERE status = 'completed'
                """)).fetchone()[0]
                
                # 失败任务数
                failed_tasks = session.execute(text("""
                    SELECT COUNT(*) FROM download_progress WHERE status = 'failed'
                """)).fetchone()[0]
                
                # 总下载记录数
                total_records = session.execute(text("""
                    SELECT SUM(downloaded_records) FROM download_progress
                """)).fetchone()[0] or 0
                
                # 按数据类型统计
                type_stats = session.execute(text("""
                    SELECT data_type, COUNT(*) as task_count, 
                           SUM(downloaded_records) as total_records,
                           SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_count
                    FROM download_progress 
                    GROUP BY data_type
                """)).fetchall()
                
                type_statistics = {}
                for row in type_stats:
                    type_statistics[row[0]] = {
                        'task_count': row[1],
                        'total_records': row[2],
                        'completed_count': row[3]
                    }
                
                return {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'failed_tasks': failed_tasks,
                    'running_tasks': total_tasks - completed_tasks - failed_tasks,
                    'total_records': total_records,
                    'completion_rate': completed_tasks / total_tasks * 100 if total_tasks > 0 else 0,
                    'type_statistics': type_statistics
                }
                
        except Exception as e:
            self.log_error(f"获取下载统计失败", e)
            return {}
    
    def cleanup_completed_tasks(self, days: int = 30):
        """清理已完成的旧任务"""
        try:
            with self.db_manager.get_session() as session:
                result = session.execute(text("""
                    DELETE FROM download_progress 
                    WHERE status = 'completed' 
                    AND completed_at < datetime('now', '-{} days')
                """.format(days)))
                
                session.commit()
                
                deleted_count = result.rowcount
                self.log_info(f"清理了 {deleted_count} 个已完成的旧任务")
                
        except Exception as e:
            self.log_error(f"清理已完成任务失败", e)
