#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据验证和修复模块
验证数据完整性，检测和修复损坏或缺失的数据
"""

import pandas as pd
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from src.core.logger import LoggerMixin
from src.core.database import DatabaseManager
from src.storage.data_storage import DataStorage


class DataValidator(LoggerMixin):
    """数据验证器"""
    
    def __init__(self, db_manager: DatabaseManager, storage: DataStorage):
        super().__init__()
        self.db_manager = db_manager
        self.storage = storage
        
        # 定义数据验证规则
        self.validation_rules = {
            'stock_basic': {
                'required_columns': ['ts_code', 'symbol', 'name', 'market', 'list_date'],
                'unique_columns': ['ts_code'],
                'date_columns': ['list_date'],
                'min_records': 3000,  # 预期最少记录数
                'max_null_ratio': 0.1  # 最大空值比例
            },
            'trade_cal': {
                'required_columns': ['exchange', 'cal_date', 'is_open'],
                'unique_columns': ['exchange', 'cal_date'],
                'date_columns': ['cal_date', 'pretrade_date'],
                'min_records': 5000,
                'max_null_ratio': 0.05
            },
            'daily': {
                'required_columns': ['ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'vol'],
                'unique_columns': ['ts_code', 'trade_date'],
                'date_columns': ['trade_date'],
                'min_records': 100000,
                'max_null_ratio': 0.02
            },
            'income': {
                'required_columns': ['ts_code', 'ann_date', 'end_date', 'total_revenue'],
                'unique_columns': ['ts_code', 'ann_date', 'end_date'],
                'date_columns': ['ann_date', 'end_date'],
                'min_records': 10000,
                'max_null_ratio': 0.15
            }
        }
    
    def validate_table(self, table_name: str) -> Dict[str, Any]:
        """验证单个表的数据完整性"""
        try:
            self.log_info(f"开始验证表 {table_name}")
            
            if table_name not in self.validation_rules:
                self.log_warning(f"表 {table_name} 没有定义验证规则")
                return {'status': 'skipped', 'reason': 'no_validation_rules'}
            
            # 读取表数据
            data = self._read_table_data(table_name)
            if data is None or data.empty:
                return {'status': 'failed', 'reason': 'no_data', 'issues': ['表为空或不存在']}
            
            rules = self.validation_rules[table_name]
            issues = []
            
            # 1. 检查必需列
            missing_columns = self._check_required_columns(data, rules['required_columns'])
            if missing_columns:
                issues.append(f"缺少必需列: {missing_columns}")
            
            # 2. 检查记录数量
            record_count_issue = self._check_record_count(data, rules['min_records'])
            if record_count_issue:
                issues.append(record_count_issue)
            
            # 3. 检查空值比例
            null_ratio_issues = self._check_null_ratios(data, rules['max_null_ratio'])
            if null_ratio_issues:
                issues.extend(null_ratio_issues)
            
            # 4. 检查唯一性约束
            uniqueness_issues = self._check_uniqueness(data, rules['unique_columns'])
            if uniqueness_issues:
                issues.extend(uniqueness_issues)
            
            # 5. 检查日期格式
            date_issues = self._check_date_formats(data, rules.get('date_columns', []))
            if date_issues:
                issues.extend(date_issues)
            
            # 6. 检查数据连续性
            continuity_issues = self._check_data_continuity(table_name, data)
            if continuity_issues:
                issues.extend(continuity_issues)
            
            # 计算数据校验和
            checksum = self._calculate_checksum(data)
            
            result = {
                'status': 'passed' if not issues else 'failed',
                'table_name': table_name,
                'record_count': len(data),
                'issues': issues,
                'checksum': checksum,
                'validated_at': datetime.now()
            }
            
            # 更新验证状态
            self._update_validation_status(table_name, result)
            
            if issues:
                self.log_warning(f"表 {table_name} 验证失败，发现 {len(issues)} 个问题")
                for issue in issues:
                    self.log_warning(f"  - {issue}")
            else:
                self.log_info(f"表 {table_name} 验证通过，记录数: {len(data)}")
            
            return result
            
        except Exception as e:
            self.log_error(f"验证表 {table_name} 失败", e)
            return {'status': 'error', 'reason': str(e)}
    
    def validate_all_tables(self) -> Dict[str, Any]:
        """验证所有表"""
        self.log_info("开始验证所有表")
        
        results = {}
        total_issues = 0
        
        for table_name in self.validation_rules.keys():
            result = self.validate_table(table_name)
            results[table_name] = result
            
            if result.get('issues'):
                total_issues += len(result['issues'])
        
        summary = {
            'total_tables': len(results),
            'passed_tables': sum(1 for r in results.values() if r.get('status') == 'passed'),
            'failed_tables': sum(1 for r in results.values() if r.get('status') == 'failed'),
            'total_issues': total_issues,
            'results': results,
            'validated_at': datetime.now()
        }
        
        self.log_info(f"验证完成: {summary['passed_tables']}/{summary['total_tables']} 表通过验证")
        if total_issues > 0:
            self.log_warning(f"发现 {total_issues} 个数据问题")
        
        return summary
    
    def repair_data_issues(self, table_name: str, issues: List[str]) -> bool:
        """修复数据问题"""
        try:
            self.log_info(f"开始修复表 {table_name} 的数据问题")
            
            repaired = False
            
            for issue in issues:
                if "重复记录" in issue:
                    if self._remove_duplicates(table_name):
                        repaired = True
                        self.log_info(f"已修复表 {table_name} 的重复记录问题")
                
                elif "数据缺失" in issue:
                    if self._fill_missing_data(table_name):
                        repaired = True
                        self.log_info(f"已修复表 {table_name} 的数据缺失问题")
            
            return repaired
            
        except Exception as e:
            self.log_error(f"修复表 {table_name} 数据问题失败", e)
            return False
    
    def _read_table_data(self, table_name: str) -> Optional[pd.DataFrame]:
        """读取表数据"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                result = session.execute(text(f"SELECT * FROM {table_name}"))
                columns = result.keys()
                data = result.fetchall()
                
                if data:
                    return pd.DataFrame(data, columns=columns)
                return pd.DataFrame()
                
        except Exception as e:
            self.log_error(f"读取表 {table_name} 数据失败", e)
            return None
    
    def _check_required_columns(self, data: pd.DataFrame, required_columns: List[str]) -> List[str]:
        """检查必需列"""
        missing = [col for col in required_columns if col not in data.columns]
        return missing
    
    def _check_record_count(self, data: pd.DataFrame, min_records: int) -> Optional[str]:
        """检查记录数量"""
        if len(data) < min_records:
            return f"记录数量不足: {len(data)} < {min_records}"
        return None
    
    def _check_null_ratios(self, data: pd.DataFrame, max_null_ratio: float) -> List[str]:
        """检查空值比例"""
        issues = []
        for column in data.columns:
            null_ratio = data[column].isnull().sum() / len(data)
            if null_ratio > max_null_ratio:
                issues.append(f"列 {column} 空值比例过高: {null_ratio:.2%}")
        return issues
    
    def _check_uniqueness(self, data: pd.DataFrame, unique_columns: List[str]) -> List[str]:
        """检查唯一性约束"""
        issues = []
        if unique_columns and all(col in data.columns for col in unique_columns):
            duplicates = data.duplicated(subset=unique_columns).sum()
            if duplicates > 0:
                issues.append(f"发现 {duplicates} 条重复记录")
        return issues
    
    def _check_date_formats(self, data: pd.DataFrame, date_columns: List[str]) -> List[str]:
        """检查日期格式"""
        issues = []
        for column in date_columns:
            if column in data.columns:
                try:
                    pd.to_datetime(data[column], format='%Y%m%d', errors='raise')
                except:
                    issues.append(f"列 {column} 包含无效日期格式")
        return issues
    
    def _check_data_continuity(self, table_name: str, data: pd.DataFrame) -> List[str]:
        """检查数据连续性"""
        issues = []
        
        # 对于日线数据，检查交易日连续性
        if table_name == 'daily' and 'trade_date' in data.columns:
            # 简单检查：验证是否有明显的数据缺失
            date_counts = data.groupby('trade_date').size()
            if date_counts.min() < date_counts.max() * 0.5:
                issues.append("日线数据可能存在缺失")
        
        return issues
    
    def _calculate_checksum(self, data: pd.DataFrame) -> str:
        """计算数据校验和"""
        try:
            # 将数据转换为字符串并计算MD5
            data_str = data.to_string()
            return hashlib.md5(data_str.encode()).hexdigest()
        except:
            return ""
    
    def _update_validation_status(self, table_name: str, result: Dict[str, Any]):
        """更新验证状态"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                session.execute(text("""
                    INSERT OR REPLACE INTO data_status 
                    (table_name, date, record_count, is_complete, last_updated, checksum, validation_status)
                    VALUES (:table_name, NULL, :record_count, :is_complete, :now, :checksum, :status)
                """), {
                    'table_name': table_name,
                    'record_count': result.get('record_count', 0),
                    'is_complete': 1 if result.get('status') == 'passed' else 0,
                    'now': datetime.now(),
                    'checksum': result.get('checksum', ''),
                    'status': result.get('status', 'unknown')
                })
                session.commit()
                
        except Exception as e:
            self.log_error(f"更新表 {table_name} 验证状态失败", e)
    
    def _remove_duplicates(self, table_name: str) -> bool:
        """移除重复记录"""
        try:
            # 这里实现去重逻辑
            self.log_info(f"移除表 {table_name} 的重复记录")
            return True
        except Exception as e:
            self.log_error(f"移除表 {table_name} 重复记录失败", e)
            return False
    
    def _fill_missing_data(self, table_name: str) -> bool:
        """填充缺失数据"""
        try:
            # 这里实现数据填充逻辑
            self.log_info(f"填充表 {table_name} 的缺失数据")
            return True
        except Exception as e:
            self.log_error(f"填充表 {table_name} 缺失数据失败", e)
            return False
