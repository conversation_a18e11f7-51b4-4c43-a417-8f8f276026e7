#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据下载器主模块
负责协调各种数据的下载任务
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

from ..core.tushare_api import TushareAPI
from ..core.logger import LoggerMixin
from ..storage.data_storage import DataStorage
from .basic_data_downloader import BasicDataDownloader
from .market_data_downloader import MarketDataDownloader
from .financial_data_downloader import FinancialDataDownloader
from .index_data_downloader import IndexDataDownloader


class DataDownloader(LoggerMixin):
    """数据下载器主类"""
    
    def __init__(self, config, db_manager, progress_tracker):
        """初始化数据下载器"""
        self.config = config
        self.db_manager = db_manager
        self.progress_tracker = progress_tracker
        
        # 初始化API客户端
        self.api = TushareAPI(config)
        
        # 初始化数据存储
        self.storage = DataStorage(db_manager, config)
        
        # 初始化各类数据下载器
        self.basic_downloader = BasicDataDownloader(self.api, self.storage, self.progress_tracker, config)
        self.market_downloader = MarketDataDownloader(self.api, self.storage, self.progress_tracker, config)
        self.financial_downloader = FinancialDataDownloader(self.api, self.storage, self.progress_tracker, config)
        self.index_downloader = IndexDataDownloader(self.api, self.storage, self.progress_tracker, config)
        
        # 下载配置
        self.download_config = config.get_download_config()
        self.max_workers = self.download_config.get('max_workers', 3)
        
        self.log_info("数据下载器初始化完成")
    
    def download_data(self, data_type: str = 'all', 
                     start_date: Optional[str] = None,
                     end_date: Optional[str] = None,
                     resume: bool = False):
        """下载数据主入口"""
        try:
            self.log_info(f"开始下载数据，类型: {data_type}, 日期范围: {start_date} - {end_date}")
            
            if resume:
                self.log_info("启用断点续传模式")
                self._resume_incomplete_tasks()
            
            if data_type == 'all':
                self._download_all_data(start_date, end_date)
            elif data_type == 'basic':
                self._download_basic_data()
            elif data_type == 'daily':
                self._download_market_data(start_date, end_date)
            elif data_type == 'financial':
                self._download_financial_data(start_date, end_date)
            elif data_type == 'index':
                self._download_index_data(start_date, end_date)
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
            
            self.log_info("数据下载完成")
            self._print_download_summary()
            
        except Exception as e:
            self.log_error(f"数据下载失败", e)
            raise
    
    def _download_all_data(self, start_date: Optional[str], end_date: Optional[str]):
        """下载所有数据"""
        self.log_info("开始下载所有数据")
        
        # 按优先级下载
        # 1. 基础数据（必须先下载）
        self.log_info("第1阶段: 下载基础数据")
        self._download_basic_data()
        
        # 2. 行情数据
        self.log_info("第2阶段: 下载行情数据")
        self._download_market_data(start_date, end_date)
        
        # 3. 财务数据
        self.log_info("第3阶段: 下载财务数据")
        self._download_financial_data(start_date, end_date)
        
        # 4. 指数数据
        self.log_info("第4阶段: 下载指数数据")
        self._download_index_data(start_date, end_date)
    
    def _download_basic_data(self):
        """下载基础数据"""
        try:
            self.log_info("开始下载基础数据")
            
            # 下载股票列表
            self.basic_downloader.download_stock_basic()
            
            # 下载交易日历
            self.basic_downloader.download_trade_calendar()
            
            # 下载公司基本信息
            self.basic_downloader.download_stock_company()
            
            # 下载其他基础数据
            self.basic_downloader.download_hs_const()
            self.basic_downloader.download_concept_data()
            
            self.log_info("基础数据下载完成")
            
        except Exception as e:
            self.log_error(f"基础数据下载失败", e)
            raise
    
    def _download_market_data(self, start_date: Optional[str], end_date: Optional[str]):
        """下载行情数据"""
        try:
            self.log_info("开始下载行情数据")
            
            # 获取股票列表
            stock_list = self.storage.get_stock_list()
            if not stock_list:
                self.log_warning("未找到股票列表，请先下载基础数据")
                return
            
            # 下载日线数据
            self.market_downloader.download_daily_data(stock_list, start_date, end_date)
            
            # 下载每日指标
            self.market_downloader.download_daily_basic(stock_list, start_date, end_date)
            
            # 下载复权因子
            self.market_downloader.download_adj_factor(stock_list, start_date, end_date)
            
            # 下载停牌信息
            self.market_downloader.download_suspend_data(start_date, end_date)
            
            self.log_info("行情数据下载完成")
            
        except Exception as e:
            self.log_error(f"行情数据下载失败", e)
            raise
    
    def _download_financial_data(self, start_date: Optional[str], end_date: Optional[str]):
        """下载财务数据"""
        try:
            self.log_info("开始下载财务数据")
            
            # 获取股票列表
            stock_list = self.storage.get_stock_list()
            if not stock_list:
                self.log_warning("未找到股票列表，请先下载基础数据")
                return
            
            # 下载利润表
            self.financial_downloader.download_income_data(stock_list, start_date, end_date)
            
            # 下载资产负债表
            self.financial_downloader.download_balance_sheet(stock_list, start_date, end_date)
            
            # 下载现金流量表
            self.financial_downloader.download_cashflow_data(stock_list, start_date, end_date)
            
            # 下载财务指标
            self.financial_downloader.download_financial_indicators(stock_list, start_date, end_date)
            
            # 下载业绩预告和快报
            self.financial_downloader.download_forecast_data(stock_list, start_date, end_date)
            
            # 下载分红送股
            self.financial_downloader.download_dividend_data(stock_list, start_date, end_date)
            
            self.log_info("财务数据下载完成")
            
        except Exception as e:
            self.log_error(f"财务数据下载失败", e)
            raise
    
    def _download_index_data(self, start_date: Optional[str], end_date: Optional[str]):
        """下载指数数据"""
        try:
            self.log_info("开始下载指数数据")
            
            # 下载指数基本信息
            self.index_downloader.download_index_basic()
            
            # 获取指数列表
            index_list = self.storage.get_index_list()
            if not index_list:
                self.log_warning("未找到指数列表")
                return
            
            # 下载指数日线数据
            self.index_downloader.download_index_daily(index_list, start_date, end_date)
            
            # 下载指数每日指标
            self.index_downloader.download_index_daily_basic(index_list, start_date, end_date)
            
            # 下载指数成分和权重
            self.index_downloader.download_index_weight(index_list, start_date, end_date)
            
            self.log_info("指数数据下载完成")
            
        except Exception as e:
            self.log_error(f"指数数据下载失败", e)
            raise
    
    def _resume_incomplete_tasks(self):
        """恢复未完成的任务"""
        try:
            incomplete_tasks = self.progress_tracker.get_incomplete_tasks()
            
            if not incomplete_tasks:
                self.log_info("没有找到未完成的任务")
                return
            
            self.log_info(f"找到 {len(incomplete_tasks)} 个未完成的任务")
            
            for task in incomplete_tasks:
                self.log_info(f"恢复任务: {task['data_type']}.{task['table_name']}")
                # 这里可以根据任务类型调用相应的下载器继续下载
                # 具体实现需要在各个下载器中添加resume功能
                
        except Exception as e:
            self.log_error(f"恢复未完成任务失败", e)
    
    def validate_data(self):
        """验证数据完整性"""
        try:
            self.log_info("开始验证数据完整性")
            
            # 验证基础数据
            self.basic_downloader.validate_data()
            
            # 验证行情数据
            self.market_downloader.validate_data()
            
            # 验证财务数据
            self.financial_downloader.validate_data()
            
            # 验证指数数据
            self.index_downloader.validate_data()
            
            self.log_info("数据验证完成")
            
        except Exception as e:
            self.log_error(f"数据验证失败", e)
            raise
    
    def repair_data(self):
        """修复缺失或损坏的数据"""
        try:
            self.log_info("开始修复数据")
            
            # 修复基础数据
            self.basic_downloader.repair_data()
            
            # 修复行情数据
            self.market_downloader.repair_data()
            
            # 修复财务数据
            self.financial_downloader.repair_data()
            
            # 修复指数数据
            self.index_downloader.repair_data()
            
            self.log_info("数据修复完成")
            
        except Exception as e:
            self.log_error(f"数据修复失败", e)
            raise
    
    def _print_download_summary(self):
        """打印下载摘要"""
        try:
            stats = self.progress_tracker.get_download_statistics()
            
            self.log_info("=== 下载摘要 ===")
            self.log_info(f"总任务数: {stats.get('total_tasks', 0)}")
            self.log_info(f"已完成: {stats.get('completed_tasks', 0)}")
            self.log_info(f"失败: {stats.get('failed_tasks', 0)}")
            self.log_info(f"运行中: {stats.get('running_tasks', 0)}")
            self.log_info(f"总记录数: {stats.get('total_records', 0):,}")
            self.log_info(f"完成率: {stats.get('completion_rate', 0):.1f}%")
            
            # 按类型统计
            type_stats = stats.get('type_statistics', {})
            if type_stats:
                self.log_info("\n按数据类型统计:")
                for data_type, stat in type_stats.items():
                    self.log_info(f"  {data_type}: {stat['completed_count']}/{stat['task_count']} 任务, "
                                f"{stat['total_records']:,} 记录")
            
        except Exception as e:
            self.log_error(f"生成下载摘要失败", e)
