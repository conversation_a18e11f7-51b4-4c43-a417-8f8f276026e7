import sqlite3

conn = sqlite3.connect('data/tushare_data.db')
cursor = conn.cursor()

# 查看所有表
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print('数据库中的表:')
for table in tables:
    print(f'  - {table[0]}')

# 检查daily_basic表
print('\n检查daily_basic表:')
try:
    cursor.execute('SELECT COUNT(*) FROM daily_basic')
    count = cursor.fetchone()[0]
    print(f'daily_basic表记录数: {count}')
except Exception as e:
    print(f'daily_basic表不存在或查询失败: {e}')

conn.close()
