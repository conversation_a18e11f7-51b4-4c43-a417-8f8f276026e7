daily_basic#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
tushare数据查看网页
简单易用的数据查看界面
"""

from flask import Flask, render_template, jsonify, request
import sqlite3
import pandas as pd
import json
from datetime import datetime
import os
import threading

app = Flask(__name__)

# 延迟导入download_manager
download_manager = None

def get_download_manager():
    global download_manager
    if download_manager is None:
        try:
            from web_download_manager import WebDownloadManager
            download_manager = WebDownloadManager()
        except Exception as e:
            print(f"Warning: Could not initialize download manager: {e}")
            download_manager = None
    return download_manager

def get_db_connection():
    """获取数据库连接"""
    db_path = 'data/tushare_data.db'
    if not os.path.exists(db_path):
        return None
    return sqlite3.connect(db_path)

def get_table_info():
    """获取所有表的信息"""
    # tushare表名对应的中文名称和官方文档链接
    table_mapping = {
        'stock_basic': {
            'chinese_name': '股票基础信息',
            'description': '股票代码、名称、上市日期等基础信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=25'
        },
        'trade_cal': {
            'chinese_name': '交易日历',
            'description': '交易所交易日历，开市/休市信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=26'
        },
        'daily': {
            'chinese_name': '日线行情',
            'description': '股票日线行情数据（开高低收、成交量等）',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=27'
        },
        'income': {
            'chinese_name': '利润表',
            'description': '上市公司财务数据-利润表',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=33'
        },
        'balancesheet': {
            'chinese_name': '资产负债表',
            'description': '上市公司财务数据-资产负债表',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=36'
        },
        'cashflow': {
            'chinese_name': '现金流量表',
            'description': '上市公司财务数据-现金流量表',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=44'
        },
        'index_basic': {
            'chinese_name': '指数基础信息',
            'description': '指数代码、名称等基础信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=94'
        },
        'index_daily': {
            'chinese_name': '指数日线行情',
            'description': '指数日线行情数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=95'
        },
        'stock_company': {
            'chinese_name': '上市公司基本信息',
            'description': '上市公司基本信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=112'
        },
        'hs_const': {
            'chinese_name': '沪深港通成份股',
            'description': '沪深港通成份股信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=104'
        },
        'concept': {
            'chinese_name': '概念股分类',
            'description': '概念股分类数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=125'
        },
        'download_progress': {
            'chinese_name': '下载进度表',
            'description': '系统内部进度跟踪表',
            'tushare_url': None
        },
        'data_status': {
            'chinese_name': '数据状态表',
            'description': '系统内部数据状态管理表',
            'tushare_url': None
        },
        'error_logs': {
            'chinese_name': '错误日志表',
            'description': '系统内部错误记录表',
            'tushare_url': None
        }
    }

    conn = get_db_connection()
    if not conn:
        return []

    try:
        cursor = conn.cursor()

        # 获取所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()

        table_info = []
        for table in tables:
            table_name = table[0]
            try:
                # 获取记录数
                cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                count = cursor.fetchone()[0]

                # 获取表结构
                cursor.execute(f'PRAGMA table_info({table_name})')
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]

                # 获取最新记录时间（如果有日期列）
                latest_date = None
                date_columns = ['trade_date', 'cal_date', 'end_date', 'ann_date']
                for date_col in date_columns:
                    if date_col in column_names:
                        try:
                            cursor.execute(f'SELECT MAX({date_col}) FROM {table_name}')
                            latest_date = cursor.fetchone()[0]
                            break
                        except:
                            continue

                # 获取表的中文信息
                table_meta = table_mapping.get(table_name, {
                    'chinese_name': table_name,
                    'description': '未知数据表',
                    'tushare_url': None
                })

                table_info.append({
                    'name': table_name,
                    'chinese_name': table_meta['chinese_name'],
                    'description': table_meta['description'],
                    'tushare_url': table_meta['tushare_url'],
                    'count': count,
                    'columns': column_names,
                    'latest_date': latest_date
                })
            except Exception as e:
                table_info.append({
                    'name': table_name,
                    'chinese_name': table_mapping.get(table_name, {}).get('chinese_name', table_name),
                    'description': '数据加载失败',
                    'tushare_url': table_mapping.get(table_name, {}).get('tushare_url'),
                    'count': 0,
                    'columns': [],
                    'latest_date': None,
                    'error': str(e)
                })

        conn.close()
        return table_info
    except Exception as e:
        conn.close()
        return []

def get_download_progress():
    """获取下载进度"""
    conn = get_db_connection()
    if not conn:
        return {'total': 0, 'completed': 0, 'progress': 0}
    
    try:
        cursor = conn.cursor()
        
        # 检查是否有进度表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='download_progress'")
        if not cursor.fetchone():
            return {'total': 0, 'completed': 0, 'progress': 0}
        
        # 获取进度统计
        cursor.execute("SELECT status, COUNT(*) FROM download_progress GROUP BY status")
        status_counts = dict(cursor.fetchall())
        
        total = sum(status_counts.values())
        completed = status_counts.get('completed', 0)
        progress = (completed / total * 100) if total > 0 else 0
        
        conn.close()
        return {
            'total': total,
            'completed': completed,
            'progress': round(progress, 1),
            'status_counts': status_counts
        }
    except Exception as e:
        conn.close()
        return {'total': 0, 'completed': 0, 'progress': 0, 'error': str(e)}

@app.route('/')
def index():
    """主页"""
    return render_template('dashboard.html')

@app.route('/api/tables')
def api_tables():
    """获取表信息API"""
    tables = get_table_info()
    return jsonify(tables)

@app.route('/api/progress')
def api_progress():
    """获取进度信息API"""
    progress = get_download_progress()
    return jsonify(progress)

@app.route('/api/table/<table_name>')
def api_table_data(table_name):
    """获取表数据API"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': '数据库连接失败'})
    
    try:
        # 获取分页参数
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))
        offset = (page - 1) * per_page
        
        # 获取总记录数
        cursor = conn.cursor()
        cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
        total = cursor.fetchone()[0]
        
        # 获取数据
        cursor.execute(f'SELECT * FROM {table_name} LIMIT {per_page} OFFSET {offset}')
        rows = cursor.fetchall()
        
        # 获取列名
        cursor.execute(f'PRAGMA table_info({table_name})')
        columns = [col[1] for col in cursor.fetchall()]
        
        # 转换为字典列表
        data = []
        for row in rows:
            data.append(dict(zip(columns, row)))
        
        conn.close()
        
        return jsonify({
            'data': data,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page
        })
    
    except Exception as e:
        conn.close()
        return jsonify({'error': str(e)})

@app.route('/api/search/<table_name>')
def api_search_table(table_name):
    """搜索表数据API"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': '数据库连接失败'})
    
    try:
        search_term = request.args.get('q', '')
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 100))
        offset = (page - 1) * per_page
        
        cursor = conn.cursor()
        
        # 获取列名
        cursor.execute(f'PRAGMA table_info({table_name})')
        columns = [col[1] for col in cursor.fetchall()]
        
        if search_term:
            # 构建搜索条件
            search_conditions = []
            for col in columns:
                search_conditions.append(f"{col} LIKE '%{search_term}%'")
            where_clause = " OR ".join(search_conditions)
            
            # 获取搜索结果总数
            cursor.execute(f'SELECT COUNT(*) FROM {table_name} WHERE {where_clause}')
            total = cursor.fetchone()[0]
            
            # 获取搜索结果
            cursor.execute(f'SELECT * FROM {table_name} WHERE {where_clause} LIMIT {per_page} OFFSET {offset}')
        else:
            # 没有搜索词，返回所有数据
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            total = cursor.fetchone()[0]
            cursor.execute(f'SELECT * FROM {table_name} LIMIT {per_page} OFFSET {offset}')
        
        rows = cursor.fetchall()
        
        # 转换为字典列表
        data = []
        for row in rows:
            data.append(dict(zip(columns, row)))
        
        conn.close()
        
        return jsonify({
            'data': data,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page,
            'search_term': search_term
        })
    
    except Exception as e:
        conn.close()
        return jsonify({'error': str(e)})

# ==================== 新增下载管理API ====================

@app.route('/api/download/single', methods=['POST'])
def download_single_table():
    """下载单个表"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        data = request.get_json()
        table_name = data.get('table_name')
        mode = data.get('mode', 'full')  # full 或 incremental
        start_date = data.get('start_date')  # 开始日期
        end_date = data.get('end_date')      # 结束日期

        # 调试信息
        print(f"[DEBUG] 接收到下载请求: table_name={table_name}, mode={mode}, start_date={start_date}, end_date={end_date}")

        # 转换日期格式：从 YYYY-MM-DD 转换为 YYYYMMDD
        if start_date:
            start_date = start_date.replace('-', '')
            print(f"[DEBUG] 转换后的开始日期: {start_date}")
        if end_date:
            end_date = end_date.replace('-', '')
            print(f"[DEBUG] 转换后的结束日期: {end_date}")

        if not table_name:
            return jsonify({'success': False, 'message': '缺少表名参数'})

        result = dm.download_single_table(table_name, mode, start_date, end_date)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/download/batch', methods=['POST'])
def download_batch():
    """批量下载操作"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        data = request.get_json()
        operation = data.get('operation')  # 'update_all' 或 'download_missing'

        if operation == 'update_all':
            # 更新所有已下载的表
            result = dm.update_all_tables()
        elif operation == 'download_missing':
            # 下载所有缺失的表
            result = dm.download_missing_tables()
        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'})

        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/download/status')
def get_download_status():
    """获取下载状态"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        task_id = request.args.get('task_id')

        if task_id:
            # 获取特定任务状态
            status = dm.get_download_status(task_id)
            if status:
                return jsonify({'success': True, 'status': status})
            else:
                return jsonify({'success': False, 'message': '任务不存在'})
        else:
            # 获取所有任务状态
            all_status = dm.get_all_download_status()
            return jsonify({'success': True, 'status': all_status})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/download/logs')
def get_operation_logs():
    """获取操作日志"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': True, 'logs': []})

        limit = request.args.get('limit', 50, type=int)
        logs = dm.get_operation_logs(limit)
        return jsonify({'success': True, 'logs': logs})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/download/cancel', methods=['POST'])
def cancel_download():
    """取消下载任务"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        data = request.get_json()
        task_id = data.get('task_id')

        if not task_id:
            return jsonify({'success': False, 'message': '缺少任务ID参数'})

        result = dm.cancel_download(task_id)
        return jsonify(result)

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/tables/enhanced')
def get_enhanced_table_info():
    """获取增强的表信息（包含详细状态）"""
    try:
        tables = []
        table_mapping = get_table_mapping()

        for table_name, info in table_mapping.items():
            # 获取详细状态
            dm = get_download_manager()
            status = dm.get_table_status(table_name) if dm else 'unknown'

            # 获取记录数和列信息
            count = 0
            columns = []
            conn = get_db_connection()
            if conn:
                try:
                    cursor = conn.cursor()
                    # 获取记录数
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]

                    # 获取列信息
                    cursor.execute(f'PRAGMA table_info({table_name})')
                    columns = [row[1] for row in cursor.fetchall()]
                except:
                    count = 0
                    columns = []
                finally:
                    conn.close()

            # 状态映射
            status_mapping = {
                'downloaded': {'text': '已下载', 'class': 'downloaded'},
                'needs_update': {'text': '需要更新', 'class': 'needs-update'},
                'empty': {'text': '未下载', 'class': 'empty'},
                'downloading': {'text': '下载中', 'class': 'downloading'},
                'not_exists': {'text': '未下载', 'class': 'empty'},
                'error': {'text': '错误', 'class': 'error'},
                'unknown': {'text': '未知', 'class': 'unknown'}
            }

            status_info = status_mapping.get(status, status_mapping['unknown'])

            tables.append({
                'name': table_name,
                'chinese_name': info['chinese_name'],
                'description': info['description'],
                'count': count,
                'columns': columns,
                'status': status,
                'status_text': status_info['text'],
                'status_class': status_info['class'],
                'tushare_url': info['tushare_url']
            })

        return jsonify(tables)

    except Exception as e:
        return jsonify({'error': str(e)})

def get_table_mapping():
    """获取表映射信息"""
    return {
        # 基础数据
        'stock_basic': {
            'chinese_name': '股票基础信息',
            'description': '股票代码、名称、上市日期等基础信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=25'
        },
        'trade_cal': {
            'chinese_name': '交易日历',
            'description': '交易所交易日历，开市/休市信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=26'
        },
        'stock_company': {
            'chinese_name': '上市公司信息',
            'description': '上市公司基本信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=112'
        },
        'hs_const': {
            'chinese_name': '沪深港通成份股',
            'description': '沪深港通成份股信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=104'
        },
        'concept': {
            'chinese_name': '概念股分类',
            'description': '概念股分类数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=125'
        },
        'concept_detail': {
            'chinese_name': '概念股明细',
            'description': '概念股成分股明细数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=126'
        },
        'namechange': {
            'chinese_name': '股票曾用名',
            'description': '股票历史名称变更记录',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=100'
        },
        'new_share': {
            'chinese_name': '新股列表',
            'description': '新股上市信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=123'
        },
        # 市场数据
        'daily': {
            'chinese_name': '日线行情',
            'description': '股票日线行情数据（开高低收等）',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=27'
        },
        'daily_basic': {
            'chinese_name': '每日指标',
            'description': '股票每日技术指标数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=32'
        },
        'adj_factor': {
            'chinese_name': '复权因子',
            'description': '股票复权因子数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=28'
        },
        'suspend_d': {
            'chinese_name': '停牌信息',
            'description': '股票停牌复牌信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=31'
        },
        'moneyflow': {
            'chinese_name': '资金流向',
            'description': '个股资金流向数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=170'
        },
        'stk_limit': {
            'chinese_name': '涨跌停价格',
            'description': '股票涨跌停价格数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=183'
        },
        # 财务数据
        'income': {
            'chinese_name': '利润表',
            'description': '上市公司利润表数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=33'
        },
        'balancesheet': {
            'chinese_name': '资产负债表',
            'description': '上市公司资产负债表数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=36'
        },
        'cashflow': {
            'chinese_name': '现金流量表',
            'description': '上市公司现金流量表数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=44'
        },
        'fina_indicator': {
            'chinese_name': '财务指标',
            'description': '上市公司财务指标数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=79'
        },
        'forecast': {
            'chinese_name': '业绩预告',
            'description': '上市公司业绩预告数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=45'
        },
        'express': {
            'chinese_name': '业绩快报',
            'description': '上市公司业绩快报数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=46'
        },
        'dividend': {
            'chinese_name': '分红送股',
            'description': '上市公司分红送股数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=103'
        },
        'fina_audit': {
            'chinese_name': '财务审计意见',
            'description': '上市公司财务审计意见',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=80'
        },
        'fina_mainbz': {
            'chinese_name': '主营业务构成',
            'description': '上市公司主营业务构成',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=81'
        },
        # 指数数据
        'index_basic': {
            'chinese_name': '指数基础信息',
            'description': '指数代码、名称等基础信息',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=94'
        },
        'index_daily': {
            'chinese_name': '指数日线行情',
            'description': '指数日线行情数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=95'
        },
        'index_dailybasic': {
            'chinese_name': '指数每日指标',
            'description': '指数每日技术指标数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=96'
        },
        'index_weight': {
            'chinese_name': '指数成分权重',
            'description': '指数成分股权重数据',
            'tushare_url': 'https://tushare.pro/document/2?doc_id=97'
        }
    }

if __name__ == '__main__':
    # 创建templates目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🌐 启动tushare数据查看网页...")
    print("📊 打开浏览器访问: http://localhost:5000")
    print("🔄 按 Ctrl+C 停止服务")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
