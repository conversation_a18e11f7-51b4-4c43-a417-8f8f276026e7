# tushare数据下载系统 - 高级功能文档

## 概述

本文档介绍tushare数据下载系统的高级功能，包括增量更新、数据验证、系统监控、性能优化等功能。

## 高级功能模块

### 1. 增量更新器 (IncrementalUpdater)

**文件位置**: `src/utils/incremental_updater.py`

**主要功能**:
- 智能检测需要更新的数据表
- 支持不同更新频率策略（日、周、月、季度）
- 计算增量更新的日期范围
- 避免重复下载已有数据

**使用示例**:
```python
from src.utils.incremental_updater import IncrementalUpdater

updater = IncrementalUpdater(db_manager, storage)

# 检查需要更新的表
tables_to_update = updater.get_tables_need_update()

# 获取增量更新日期范围
date_range = updater.get_incremental_date_range('daily')
if date_range:
    start_date, end_date = date_range
    print(f"增量更新范围: {start_date} - {end_date}")
```

**更新策略配置**:
- `stock_basic`: 每周更新
- `trade_cal`: 每月更新
- `daily`: 每日更新
- `income`: 每季度更新
- `index_basic`: 每月更新
- `index_daily`: 每日更新

### 2. 数据验证器 (DataValidator)

**文件位置**: `src/utils/data_validator.py`

**主要功能**:
- 基于规则的数据完整性验证
- 检查必需列、记录数量、数据质量
- 自动修复数据问题
- 生成验证报告

**使用示例**:
```python
from src.utils.data_validator import DataValidator

validator = DataValidator(db_manager, storage)

# 验证单个表
result = validator.validate_table('stock_basic')
print(f"验证结果: {result['status']}")

# 验证所有表
results = validator.validate_all_tables()
print(f"总表数: {results['total_tables']}, 通过: {results['passed_tables']}")
```

**验证规则**:
- 必需列检查
- 最小记录数验证
- 空值比例检查
- 数据格式验证

### 3. 系统监控器 (SystemMonitor)

**文件位置**: `src/utils/monitor.py`

**主要功能**:
- 下载进度监控
- 系统健康状态检查
- 性能指标统计
- 自动报告生成

**使用示例**:
```python
from src.utils.monitor import SystemMonitor

monitor = SystemMonitor(db_manager)

# 获取下载进度
progress = monitor.get_download_progress()
print(f"完成率: {progress['summary']['completion_rate']:.1%}")

# 获取系统健康状态
health = monitor.get_system_health()
print(f"系统状态: {health['status']}")

# 生成日报
report = monitor.generate_daily_report()
```

### 4. 性能优化器 (PerformanceOptimizer)

**文件位置**: `src/utils/performance_optimizer.py`

**主要功能**:
- 并发控制和批处理
- 智能重试机制
- 速率限制
- 缓存机制

**使用示例**:
```python
from src.utils.performance_optimizer import PerformanceOptimizer, PerformanceConfig

config = PerformanceConfig(
    max_workers=4,
    batch_size=100,
    retry_attempts=3,
    cache_enabled=True
)

optimizer = PerformanceOptimizer(config)

# 优化执行任务
results = optimizer.execute_with_optimization(func, tasks)

# 获取性能报告
report = optimizer.get_performance_report()
```

## 高级管理工具

**文件位置**: `advanced_manager.py`

这是一个命令行工具，整合了所有高级功能：

### 可用命令

```bash
# 查看帮助
python advanced_manager.py --help

# 检查增量更新需求
python advanced_manager.py --check-updates

# 验证数据完整性
python advanced_manager.py --validate-all
python advanced_manager.py --validate stock_basic

# 显示系统状态
python advanced_manager.py --status

# 显示性能指标
python advanced_manager.py --performance

# 生成报告
python advanced_manager.py --report daily

# 修复数据问题
python advanced_manager.py --repair stock_basic
```

### 功能演示

运行演示脚本查看所有功能：

```bash
python demo_advanced_features.py
```

## 配置说明

### 增量更新配置

在 `src/utils/incremental_updater.py` 中的 `update_strategies` 字典配置各表的更新策略：

```python
update_strategies = {
    'stock_basic': {
        'frequency': 'weekly',      # 更新频率
        'incremental': False,       # 是否支持增量更新
        'date_column': None         # 日期列名
    },
    'daily': {
        'frequency': 'daily',
        'incremental': True,
        'date_column': 'trade_date'
    }
}
```

### 验证规则配置

在 `src/utils/data_validator.py` 中的 `validation_rules` 字典配置验证规则：

```python
validation_rules = {
    'stock_basic': {
        'required_columns': ['ts_code', 'symbol', 'name'],
        'min_records': 1000,
        'max_null_percentage': 50.0
    }
}
```

## 数据库表结构

### 新增表

高级功能模块会创建以下新表：

1. **download_progress**: 下载进度跟踪
2. **data_status**: 数据状态管理
3. **error_logs**: 错误日志记录

### 表结构说明

```sql
-- 下载进度表
CREATE TABLE download_progress (
    id INTEGER PRIMARY KEY,
    data_type TEXT NOT NULL,
    status TEXT NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

-- 数据状态表
CREATE TABLE data_status (
    id INTEGER PRIMARY KEY,
    table_name TEXT NOT NULL,
    record_count INTEGER,
    is_complete BOOLEAN,
    last_updated TIMESTAMP,
    checksum TEXT
);
```

## 性能优化建议

1. **并发控制**: 根据系统资源调整 `max_workers` 参数
2. **批处理大小**: 根据内存大小调整 `batch_size` 参数
3. **缓存策略**: 启用缓存可显著提高重复查询性能
4. **速率限制**: 遵守tushare API限制，避免被封禁

## 故障排除

### 常见问题

1. **数据库锁定**: 确保没有其他进程在使用数据库
2. **内存不足**: 减少批处理大小或并发数
3. **网络超时**: 增加重试次数和延迟时间
4. **API限制**: 检查速率限制配置

### 日志查看

系统日志保存在 `logs/` 目录下：
- `app.log`: 应用日志
- `progress.log`: 进度日志
- `api.log`: API调用日志

## 扩展开发

### 添加新的验证规则

1. 在 `DataValidator` 类中添加新的验证方法
2. 在 `validation_rules` 中配置新规则
3. 更新 `validate_table` 方法调用新验证

### 添加新的监控指标

1. 在 `SystemMonitor` 类中添加新的指标收集方法
2. 更新报告生成逻辑
3. 在管理工具中添加新的显示选项

## 总结

高级功能模块为tushare数据下载系统提供了企业级的数据管理能力，包括：

- ✅ 智能增量更新，避免重复下载
- ✅ 全面数据验证，确保数据质量
- ✅ 实时系统监控，掌握运行状态
- ✅ 性能优化机制，提高下载效率
- ✅ 统一管理界面，简化操作流程

这些功能确保了系统的可靠性、高效性和可维护性。
