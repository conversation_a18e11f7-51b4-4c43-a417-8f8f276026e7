#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
手机版tushare数据查看器
简化版本，适合手机浏览
"""

from flask import Flask, render_template, jsonify, request
import sqlite3
import socket
import os

app = Flask(__name__)

# 延迟导入download_manager
download_manager = None

def get_download_manager():
    global download_manager
    if download_manager is None:
        try:
            from web_download_manager import WebDownloadManager
            download_manager = WebDownloadManager()
        except Exception as e:
            print(f"Warning: Could not initialize download manager: {e}")
            download_manager = None
    return download_manager

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 连接到一个远程地址来获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return "127.0.0.1"

def get_db_connection():
    """获取数据库连接"""
    db_path = 'data/tushare_data.db'
    if not os.path.exists(db_path):
        return None
    return sqlite3.connect(db_path)

def get_simple_table_info():
    """获取简化的表信息"""
    table_mapping = {
        'stock_basic': '股票基础信息',
        'trade_cal': '交易日历',
        'daily': '日线行情',
        'income': '利润表',
        'balancesheet': '资产负债表',
        'cashflow': '现金流量表',
        'index_basic': '指数基础信息',
        'index_daily': '指数日线行情',
        'stock_company': '上市公司信息',
        'hs_const': '沪深港通成份股',
        'concept': '概念股分类'
    }
    
    conn = get_db_connection()
    if not conn:
        return []
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = cursor.fetchall()
        
        table_info = []
        for table in tables:
            table_name = table[0]
            if table_name in table_mapping:  # 只显示tushare数据表
                try:
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                    
                    table_info.append({
                        'name': table_name,
                        'chinese_name': table_mapping[table_name],
                        'count': count,
                        'status': '已下载' if count > 0 else '未下载'
                    })
                except:
                    table_info.append({
                        'name': table_name,
                        'chinese_name': table_mapping[table_name],
                        'count': 0,
                        'status': '错误'
                    })
        
        conn.close()
        return sorted(table_info, key=lambda x: x['count'], reverse=True)
    except:
        conn.close()
        return []

@app.route('/')
def mobile_index():
    """手机版主页"""
    return render_template('mobile.html')

@app.route('/api/mobile/tables')
def api_mobile_tables():
    """手机版表信息API"""
    tables = get_simple_table_info()
    return jsonify(tables)

@app.route('/api/mobile/summary')
def api_mobile_summary():
    """手机版汇总信息"""
    tables = get_simple_table_info()
    
    total_tables = len(tables)
    downloaded_tables = len([t for t in tables if t['count'] > 0])
    total_records = sum(t['count'] for t in tables)
    
    return jsonify({
        'total_tables': total_tables,
        'downloaded_tables': downloaded_tables,
        'total_records': total_records,
        'completion_rate': round((downloaded_tables / total_tables * 100) if total_tables > 0 else 0, 1)
    })

# ==================== 新增手机版下载管理API ====================

@app.route('/api/mobile/download/single', methods=['POST'])
def mobile_download_single():
    """手机版单表下载"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        data = request.get_json()
        table_name = data.get('table_name')
        mode = data.get('mode', 'full')

        result = dm.download_single_table(table_name, mode)
        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/mobile/download/batch', methods=['POST'])
def mobile_download_batch():
    """手机版批量下载"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        data = request.get_json()
        operation = data.get('operation')

        if operation == 'update_all':
            result = dm.update_all_tables()
        elif operation == 'download_missing':
            result = dm.download_missing_tables()
        else:
            return jsonify({'success': False, 'message': '不支持的操作类型'})

        return jsonify(result)
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/mobile/download/status')
def mobile_download_status():
    """手机版下载状态"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': False, 'message': '下载管理器未初始化'})

        task_id = request.args.get('task_id')

        if task_id:
            status = dm.get_download_status(task_id)
            if status:
                return jsonify({'success': True, 'status': status})
            else:
                return jsonify({'success': False, 'message': '任务不存在'})
        else:
            all_status = dm.get_all_download_status()
            return jsonify({'success': True, 'status': all_status})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/mobile/download/logs')
def mobile_download_logs():
    """手机版操作日志"""
    try:
        dm = get_download_manager()
        if not dm:
            return jsonify({'success': True, 'logs': []})

        limit = request.args.get('limit', 10, type=int)
        logs = dm.get_operation_logs(limit)
        return jsonify({'success': True, 'logs': logs})
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/mobile/tables/enhanced')
def mobile_enhanced_tables():
    """手机版增强表信息"""
    try:
        tables = []
        table_mapping = {
            'stock_basic': '股票基础信息',
            'trade_cal': '交易日历',
            'daily': '日线行情',
            'income': '利润表',
            'balancesheet': '资产负债表',
            'cashflow': '现金流量表',
            'index_basic': '指数基础信息',
            'index_daily': '指数日线行情',
            'stock_company': '上市公司信息',
            'hs_const': '沪深港通成份股',
            'concept': '概念股分类'
        }

        for table_name, chinese_name in table_mapping.items():
            # 获取详细状态
            dm = get_download_manager()
            status = dm.get_table_status(table_name) if dm else 'unknown'

            # 获取记录数
            count = 0
            conn = get_db_connection()
            if conn:
                try:
                    cursor = conn.cursor()
                    cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
                    count = cursor.fetchone()[0]
                except:
                    count = 0
                finally:
                    conn.close()

            # 状态映射
            status_mapping = {
                'downloaded': '已下载',
                'needs_update': '需要更新',
                'empty': '未下载',
                'downloading': '下载中',
                'not_exists': '未下载',
                'error': '错误',
                'unknown': '未知'
            }

            tables.append({
                'name': table_name,
                'chinese_name': chinese_name,
                'count': count,
                'status': status,
                'status_text': status_mapping.get(status, '未知')
            })

        return jsonify(tables)
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    # 创建templates目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    local_ip = get_local_ip()
    
    print("📱 手机版tushare数据查看器启动中...")
    print(f"🌐 电脑访问: http://localhost:8080")
    print(f"📱 手机访问: http://{local_ip}:8080")
    print("🔄 按 Ctrl+C 停止服务")
    print("\n" + "="*50)
    print("📋 使用说明:")
    print("1. 确保手机和电脑在同一个WiFi网络")
    print(f"2. 在手机浏览器输入: http://{local_ip}:8080")
    print("3. 如果无法访问，请检查防火墙设置")
    print("="*50)
    
    app.run(debug=False, host='0.0.0.0', port=8080)
