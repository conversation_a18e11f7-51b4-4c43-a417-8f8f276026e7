#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据存储模块
负责数据的存储、检索和管理，保持tushare原始数据结构
"""

import pandas as pd
import hashlib
from datetime import datetime
from typing import Optional, List, Dict, Any, Union
from sqlalchemy import text

from ..core.logger import LoggerMixin


class DataStorage(LoggerMixin):
    """数据存储管理器"""
    
    def __init__(self, db_manager, config):
        """初始化数据存储管理器"""
        self.db_manager = db_manager
        self.config = config
        self.storage_config = config.get_storage_config()
        
        self.log_info("数据存储管理器初始化完成")
    
    def save_data(self, table_name: str, data: pd.DataFrame,
                  if_exists: str = 'append',
                  update_status: bool = True) -> int:
        """保存数据到数据库"""
        try:
            if data.empty:
                self.log_warning(f"表 {table_name} 的数据为空，跳过保存")
                return 0

            # 保存数据 - 使用批量保存方法避免SQLite变量限制
            record_count = self.save_data_batch(table_name, data, batch_size=1, if_exists=if_exists)

            # 更新数据状态
            if update_status:
                self._update_data_status(table_name, data, record_count)

            return record_count

        except Exception as e:
            self.log_error(f"保存数据到表 {table_name} 失败", e)
            raise
    
    def save_data_batch(self, table_name: str, data: pd.DataFrame,
                       batch_size: int = 1,
                       if_exists: str = 'append') -> int:
        """批量保存数据"""
        try:
            if data.empty:
                return 0
            
            total_records = 0
            total_batches = (len(data) + batch_size - 1) // batch_size
            
            # 逐行插入以避免SQLite变量限制
            for i in range(len(data)):
                row_data = data.iloc[i:i+1]

                if i % 100 == 0:
                    self.log_debug(f"保存记录 {i+1}/{len(data)} 到表 {table_name}")

                with self.db_manager.engine.connect() as conn:
                    row_data.to_sql(table_name, conn, if_exists=if_exists, index=False)

                total_records += 1

                # 第一行后改为append模式
                if if_exists == 'replace':
                    if_exists = 'append'
            
            self.log_info(f"批量保存完成，共 {total_records} 条记录到表 {table_name}")

            return total_records
            
        except Exception as e:
            self.log_error(f"批量保存数据到表 {table_name} 失败", e)
            raise
    
    def get_data(self, table_name: str, 
                 conditions: Optional[Dict[str, Any]] = None,
                 columns: Optional[List[str]] = None,
                 limit: Optional[int] = None) -> pd.DataFrame:
        """从数据库获取数据"""
        try:
            # 构建SQL查询
            sql_parts = []
            
            if columns:
                sql_parts.append(f"SELECT {', '.join(columns)}")
            else:
                sql_parts.append("SELECT *")
            
            sql_parts.append(f"FROM {table_name}")
            
            # 添加条件
            params = {}
            if conditions:
                where_clauses = []
                for key, value in conditions.items():
                    if isinstance(value, list):
                        placeholders = ', '.join([f':param_{key}_{i}' for i in range(len(value))])
                        where_clauses.append(f"{key} IN ({placeholders})")
                        for i, v in enumerate(value):
                            params[f'param_{key}_{i}'] = v
                    else:
                        where_clauses.append(f"{key} = :param_{key}")
                        params[f'param_{key}'] = value
                
                if where_clauses:
                    sql_parts.append(f"WHERE {' AND '.join(where_clauses)}")
            
            # 添加限制
            if limit:
                sql_parts.append(f"LIMIT {limit}")
            
            sql = ' '.join(sql_parts)
            
            # 执行查询
            with self.db_manager.engine.connect() as conn:
                result = pd.read_sql(sql, conn, params=params)
            
            self.log_debug(f"从表 {table_name} 获取 {len(result)} 条记录")
            return result
            
        except Exception as e:
            self.log_error(f"从表 {table_name} 获取数据失败", e)
            return pd.DataFrame()
    
    def check_data_exists(self, table_name: str, 
                         conditions: Optional[Dict[str, Any]] = None) -> bool:
        """检查数据是否存在"""
        try:
            with self.db_manager.get_session() as session:
                sql = f"SELECT COUNT(*) FROM {table_name}"
                params = {}
                
                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        where_clauses.append(f"{key} = :param_{key}")
                        params[f'param_{key}'] = value
                    
                    if where_clauses:
                        sql += f" WHERE {' AND '.join(where_clauses)}"
                
                result = session.execute(text(sql), params).fetchone()
                return result[0] > 0
                
        except Exception as e:
            self.log_error(f"检查表 {table_name} 数据存在性失败", e)
            return False
    
    def get_data_count(self, table_name: str, 
                      conditions: Optional[Dict[str, Any]] = None) -> int:
        """获取数据记录数"""
        try:
            with self.db_manager.get_session() as session:
                sql = f"SELECT COUNT(*) FROM {table_name}"
                params = {}
                
                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        where_clauses.append(f"{key} = :param_{key}")
                        params[f'param_{key}'] = value
                    
                    if where_clauses:
                        sql += f" WHERE {' AND '.join(where_clauses)}"
                
                result = session.execute(text(sql), params).fetchone()
                return result[0]
                
        except Exception as e:
            self.log_error(f"获取表 {table_name} 记录数失败", e)
            return 0
    
    def delete_data(self, table_name: str, 
                   conditions: Optional[Dict[str, Any]] = None) -> int:
        """删除数据"""
        try:
            with self.db_manager.get_session() as session:
                sql = f"DELETE FROM {table_name}"
                params = {}
                
                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        where_clauses.append(f"{key} = :param_{key}")
                        params[f'param_{key}'] = value
                    
                    if where_clauses:
                        sql += f" WHERE {' AND '.join(where_clauses)}"
                
                result = session.execute(text(sql), params)
                session.commit()
                
                deleted_count = result.rowcount
                self.log_info(f"从表 {table_name} 删除 {deleted_count} 条记录")
                return deleted_count
                
        except Exception as e:
            self.log_error(f"从表 {table_name} 删除数据失败", e)
            raise
    
    def get_stock_list(self) -> List[str]:
        """获取股票代码列表"""
        try:
            data = self.get_data('stock_basic', columns=['ts_code'])
            return data['ts_code'].tolist() if not data.empty else []
        except Exception as e:
            self.log_error("获取股票列表失败", e)
            return []
    
    def get_index_list(self) -> List[str]:
        """获取指数代码列表"""
        try:
            data = self.get_data('index_basic', columns=['ts_code'])
            return data['ts_code'].tolist() if not data.empty else []
        except Exception as e:
            self.log_error("获取指数列表失败", e)
            return []
    
    def get_trade_dates(self, start_date: Optional[str] = None,
                       end_date: Optional[str] = None) -> List[str]:
        """获取交易日期列表"""
        try:
            with self.db_manager.get_session() as session:
                # 构建查询条件
                query = "SELECT cal_date FROM trade_cal WHERE is_open = 1"
                params = {}

                if start_date:
                    query += " AND cal_date >= :start_date"
                    params['start_date'] = start_date

                if end_date:
                    query += " AND cal_date <= :end_date"
                    params['end_date'] = end_date

                query += " ORDER BY cal_date"

                # 执行查询
                result = session.execute(text(query), params).fetchall()
                trade_dates = [row[0] for row in result]

                self.log_debug(f"获取交易日期: {start_date} 到 {end_date}, 共 {len(trade_dates)} 个交易日")
                return trade_dates

        except Exception as e:
            self.log_error("获取交易日期失败", e)
            return []
    
    def get_latest_date(self, table_name: str, date_column: str = 'trade_date') -> Optional[str]:
        """获取表中最新的日期"""
        try:
            with self.db_manager.get_session() as session:
                result = session.execute(text(f"""
                    SELECT MAX({date_column}) FROM {table_name}
                """)).fetchone()
                
                return result[0] if result and result[0] else None
                
        except Exception as e:
            self.log_error(f"获取表 {table_name} 最新日期失败", e)
            return None
    
    def _update_data_status(self, table_name: str, data: pd.DataFrame, record_count: int):
        """更新数据状态"""
        try:
            # 计算数据校验和
            checksum = self._calculate_checksum(data)
            
            # 获取日期信息（如果有的话）
            date_value = None
            if 'trade_date' in data.columns:
                date_value = data['trade_date'].iloc[0] if not data.empty else None
            elif 'cal_date' in data.columns:
                date_value = data['cal_date'].iloc[0] if not data.empty else None
            
            with self.db_manager.get_session() as session:
                # 检查是否已存在记录
                existing = session.execute(text("""
                    SELECT id FROM data_status 
                    WHERE table_name = :table_name AND date = :date
                """), {
                    'table_name': table_name,
                    'date': date_value
                }).fetchone()
                
                if existing:
                    # 更新现有记录
                    session.execute(text("""
                        UPDATE data_status 
                        SET record_count = :record_count, 
                            is_complete = 1,
                            last_updated = :now,
                            checksum = :checksum
                        WHERE table_name = :table_name AND date = :date
                    """), {
                        'record_count': record_count,
                        'now': datetime.now(),
                        'checksum': checksum,
                        'table_name': table_name,
                        'date': date_value
                    })
                else:
                    # 插入新记录
                    session.execute(text("""
                        INSERT INTO data_status 
                        (table_name, date, record_count, is_complete, last_updated, checksum)
                        VALUES (:table_name, :date, :record_count, 1, :now, :checksum)
                    """), {
                        'table_name': table_name,
                        'date': date_value,
                        'record_count': record_count,
                        'now': datetime.now(),
                        'checksum': checksum
                    })
                
                session.commit()
                
        except Exception as e:
            self.log_error(f"更新数据状态失败", e)
    
    def _calculate_checksum(self, data: pd.DataFrame) -> str:
        """计算数据校验和"""
        try:
            # 将DataFrame转换为字符串并计算MD5
            data_str = data.to_string()
            return hashlib.md5(data_str.encode()).hexdigest()
        except Exception:
            return ""
    
    def backup_table(self, table_name: str, backup_suffix: Optional[str] = None):
        """备份数据表"""
        try:
            if not backup_suffix:
                backup_suffix = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            backup_table_name = f"{table_name}_backup_{backup_suffix}"
            
            with self.db_manager.get_session() as session:
                session.execute(text(f"""
                    CREATE TABLE {backup_table_name} AS 
                    SELECT * FROM {table_name}
                """))
                session.commit()
            
            self.log_info(f"表 {table_name} 已备份为 {backup_table_name}")
            
        except Exception as e:
            self.log_error(f"备份表 {table_name} 失败", e)
            raise
