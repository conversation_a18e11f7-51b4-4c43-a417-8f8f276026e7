#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础数据下载器
负责下载股票列表、交易日历等基础数据
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from ..core.logger import LoggerMixin


class BasicDataDownloader(LoggerMixin):
    """基础数据下载器"""
    
    def __init__(self, api, storage, progress_tracker, config):
        """初始化基础数据下载器"""
        self.api = api
        self.storage = storage
        self.progress_tracker = progress_tracker
        self.config = config
        
        self.log_info("基础数据下载器初始化完成")
    
    def download_stock_basic(self):
        """下载股票基本信息"""
        try:
            self.log_info("开始下载股票基本信息")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='stock_basic'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 下载数据
            data = self.api.get_stock_basic()
            
            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('stock_basic', data, if_exists='replace')
                
                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')
                
                self.log_info(f"股票基本信息下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("股票基本信息数据为空")
            
        except Exception as e:
            self.log_error("下载股票基本信息失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_trade_calendar(self, start_year: int = 2000):
        """下载交易日历"""
        try:
            self.log_info(f"开始下载交易日历，起始年份: {start_year}")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='trade_cal'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            current_year = datetime.now().year
            total_records = 0
            
            # 按年下载交易日历
            for year in range(start_year, current_year + 2):  # 包含下一年
                try:
                    start_date = f"{year}0101"
                    end_date = f"{year}1231"
                    
                    self.log_debug(f"下载 {year} 年交易日历")
                    
                    data = self.api.get_trade_cal(
                        exchange='',  # 空字符串表示所有交易所
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if year == start_year:
                            record_count = self.storage.save_data('trade_cal', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('trade_cal', data, if_exists='append')
                        
                        total_records += record_count
                        
                        # 更新进度
                        self.progress_tracker.update_progress(task_id, total_records)
                        
                        self.log_debug(f"{year} 年交易日历下载完成，{record_count} 条记录")
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载 {year} 年交易日历失败: {e}")
                    continue
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"交易日历下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载交易日历失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_stock_company(self):
        """下载上市公司基本信息"""
        try:
            self.log_info("开始下载上市公司基本信息")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='stock_company'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 下载数据
            data = self.api.get_stock_company()
            
            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('stock_company', data, if_exists='replace')
                
                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')
                
                self.log_info(f"上市公司基本信息下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("上市公司基本信息数据为空")
            
        except Exception as e:
            self.log_error("下载上市公司基本信息失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_hs_const(self):
        """下载沪深股通成分股"""
        try:
            self.log_info("开始下载沪深股通成分股")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='hs_const'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            total_records = 0
            
            # 下载沪股通和深股通
            for hs_type in ['SH', 'SZ']:
                try:
                    self.log_debug(f"下载{hs_type}股通成分股")
                    
                    data = self.api.get_hs_const(hs_type=hs_type)
                    
                    if not data.empty:
                        # 保存数据
                        if hs_type == 'SH':
                            record_count = self.storage.save_data('hs_const', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('hs_const', data, if_exists='append')
                        
                        total_records += record_count
                        self.log_debug(f"{hs_type}股通成分股下载完成，{record_count} 条记录")
                    
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载{hs_type}股通成分股失败: {e}")
                    continue
            
            # 更新进度和状态
            self.progress_tracker.update_progress(task_id, total_records)
            self.progress_tracker.update_task_status(task_id, 'completed')
            
            self.log_info(f"沪深股通成分股下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载沪深股通成分股失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_concept_data(self):
        """下载概念股数据"""
        try:
            self.log_info("开始下载概念股数据")
            
            # 下载概念分类
            self._download_concept_basic()
            
            # 下载概念股明细
            self._download_concept_detail()
            
        except Exception as e:
            self.log_error("下载概念股数据失败", e)
            raise
    
    def _download_concept_basic(self):
        """下载概念分类"""
        try:
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='concept'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 下载数据
            data = self.api.get_concept(src='ts')
            
            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('concept', data, if_exists='replace')
                
                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')
                
                self.log_info(f"概念分类下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("概念分类数据为空")
            
        except Exception as e:
            self.log_error("下载概念分类失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def _download_concept_detail(self):
        """下载概念股明细"""
        try:
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='concept_detail'
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 获取概念列表
            concept_data = self.storage.get_data('concept', columns=['code'])
            
            if concept_data.empty:
                self.log_warning("未找到概念分类数据，跳过概念股明细下载")
                self.progress_tracker.update_task_status(task_id, 'failed', '未找到概念分类数据')
                return
            
            total_records = 0
            concept_codes = concept_data['code'].tolist()
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(concept_codes), "下载概念股明细")
            
            for i, concept_code in enumerate(concept_codes):
                try:
                    self.log_debug(f"下载概念 {concept_code} 的成分股")
                    
                    data = self.api.get_concept_detail(id=concept_code)
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('concept_detail', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('concept_detail', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, total_records)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载概念 {concept_code} 明细失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"概念股明细下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载概念股明细失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def validate_data(self):
        """验证基础数据完整性"""
        try:
            self.log_info("开始验证基础数据完整性")
            
            # 验证股票列表
            stock_count = self.storage.get_data_count('stock_basic')
            if stock_count == 0:
                self.log_warning("股票基本信息表为空")
            else:
                self.log_info(f"股票基本信息: {stock_count} 条记录")
            
            # 验证交易日历
            trade_cal_count = self.storage.get_data_count('trade_cal')
            if trade_cal_count == 0:
                self.log_warning("交易日历表为空")
            else:
                self.log_info(f"交易日历: {trade_cal_count} 条记录")
            
            # 验证公司信息
            company_count = self.storage.get_data_count('stock_company')
            self.log_info(f"上市公司信息: {company_count} 条记录")
            
            self.log_info("基础数据验证完成")
            
        except Exception as e:
            self.log_error("验证基础数据失败", e)
            raise
    
    def download_namechange(self):
        """下载股票曾用名"""
        try:
            self.log_info("开始下载股票曾用名")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='namechange'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            # 下载数据
            data = self.api.get_namechange()

            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('namechange', data, if_exists='replace')

                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')

                self.log_info(f"股票曾用名下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("股票曾用名数据为空")

        except Exception as e:
            self.log_error("下载股票曾用名失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_new_share(self):
        """下载新股列表"""
        try:
            self.log_info("开始下载新股列表")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='new_share'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            # 下载数据
            data = self.api.get_new_share()

            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('new_share', data, if_exists='replace')

                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')

                self.log_info(f"新股列表下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("新股列表数据为空")

        except Exception as e:
            self.log_error("下载新股列表失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_concept_detail(self):
        """下载概念股明细"""
        try:
            self.log_info("开始下载概念股明细")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='basic',
                table_name='concept_detail'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            # 下载数据
            data = self.api.get_concept_detail()

            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('concept_detail', data, if_exists='replace')

                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')

                self.log_info(f"概念股明细下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("概念股明细数据为空")

        except Exception as e:
            self.log_error("下载概念股明细失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def repair_data(self):
        """修复基础数据"""
        try:
            self.log_info("开始修复基础数据")

            # 检查并修复股票列表
            if self.storage.get_data_count('stock_basic') == 0:
                self.log_info("重新下载股票基本信息")
                self.download_stock_basic()

            # 检查并修复交易日历
            if self.storage.get_data_count('trade_cal') == 0:
                self.log_info("重新下载交易日历")
                self.download_trade_calendar()

            self.log_info("基础数据修复完成")

        except Exception as e:
            self.log_error("修复基础数据失败", e)
            raise
