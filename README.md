# tushare数据下载系统

一个功能完整的tushare数据下载系统，支持断点续传、去重、完整性保证等功能。

## 🚀 主要特性

- **断点续传**：支持中断后继续下载，不丢失已下载数据
- **智能去重**：避免重复下载相同数据，节省时间和资源
- **完整性保证**：确保数据不遗漏，保持tushare原始数据结构
- **智能重试**：自动处理网络异常、API限流等问题
- **进度监控**：实时跟踪下载进度和状态
- **数据验证**：自动验证数据完整性并支持修复
- **灵活配置**：支持多种配置选项和数据库类型

## 📦 支持的数据类型

- **基础数据**：股票列表、交易日历、基础信息
- **行情数据**：日线、周线、月线数据
- **财务数据**：利润表、资产负债表、现金流量表
- **指数数据**：各种指数的行情数据
- **其他数据**：公告、新闻等

## 🛠️ 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置系统

编辑 `config.yaml` 文件，设置你的tushare token和其他配置：

```yaml
tushare:
  token: "your_tushare_token_here"
```

### 3. 初始化数据库

系统首次运行时会自动创建数据库表结构。

## 🎯 使用方法

### 基本用法

```bash
# 下载所有数据
python main.py

# 下载特定类型数据
python main.py --data-type basic

# 指定日期范围
python main.py --start-date 20230101 --end-date 20231231

# 从上次中断处继续下载
python main.py --resume

# 验证数据完整性
python main.py --validate

# 修复缺失数据
python main.py --repair
```

### 高级用法

```bash
# 使用自定义配置文件
python main.py --config my_config.yaml

# 组合使用多个选项
python main.py --data-type daily --start-date 20230101 --resume
```

## 📁 项目结构

```
tushare数据下载/
├── main.py                 # 主程序入口
├── config.yaml            # 配置文件
├── requirements.txt       # 依赖包列表
├── README.md              # 项目说明
├── src/                   # 源代码目录
│   ├── core/              # 核心模块
│   ├── downloader/        # 下载模块
│   ├── storage/           # 存储模块
│   └── utils/             # 工具模块
├── data/                  # 数据存储目录
├── logs/                  # 日志目录
└── backup/                # 备份目录
```

## ⚙️ 配置说明

主要配置项说明：

- `tushare.token`: 你的tushare API token
- `tushare.rate_limit`: API调用频率限制
- `database.type`: 数据库类型 (sqlite/postgresql)
- `download.batch_size`: 批量下载大小
- `storage.data_dir`: 数据存储目录

## 🔧 故障排除

### 常见问题

1. **API调用频率超限**
   - 调整 `config.yaml` 中的 `rate_limit` 设置
   - 系统会自动处理限流并重试

2. **网络连接问题**
   - 系统会自动重试，可调整 `retry_times` 和 `retry_delay`

3. **数据不完整**
   - 运行 `python main.py --validate` 检查数据
   - 运行 `python main.py --repair` 修复数据

## 📊 监控和日志

- 日志文件保存在 `logs/` 目录
- 支持控制台实时输出
- 提供详细的进度信息和错误报告

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

MIT License
