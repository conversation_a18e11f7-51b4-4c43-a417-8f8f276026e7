#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试高级功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.database import DatabaseManager
from src.core.logger import setup_logger
from src.storage.data_storage import DataStorage
from src.utils.incremental_updater import IncrementalUpdater
from src.utils.data_validator import DataValidator
from src.utils.monitor import SystemMonitor


def test_incremental_updater():
    """测试增量更新器"""
    print("=== 测试增量更新器 ===")
    
    try:
        config = ConfigManager()
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        storage = DataStorage(db_manager, config)
        updater = IncrementalUpdater(db_manager, storage)
        
        # 检查需要更新的表
        tables_to_update = updater.get_tables_need_update()
        print(f"需要更新的表: {tables_to_update}")
        
        # 检查增量更新范围
        for table_name in ['daily', 'trade_cal']:
            date_range = updater.get_incremental_date_range(table_name)
            if date_range:
                print(f"{table_name} 增量更新范围: {date_range[0]} - {date_range[1]}")
            else:
                print(f"{table_name} 无需增量更新")
        
        print("✅ 增量更新器测试通过")
        
    except Exception as e:
        print(f"❌ 增量更新器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_data_validator():
    """测试数据验证器"""
    print("\n=== 测试数据验证器 ===")
    
    try:
        config = ConfigManager()
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        storage = DataStorage(db_manager, config)
        validator = DataValidator(db_manager, storage)
        
        # 验证单个表
        result = validator.validate_table('stock_basic')
        print(f"stock_basic 验证结果: {result}")
        
        print("✅ 数据验证器测试通过")
        
    except Exception as e:
        print(f"❌ 数据验证器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_system_monitor():
    """测试系统监控器"""
    print("\n=== 测试系统监控器 ===")
    
    try:
        config = ConfigManager()
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        monitor = SystemMonitor(db_manager)
        
        # 获取下载进度
        progress = monitor.get_download_progress()
        if progress:
            print(f"下载进度: 总任务 {progress['summary']['total_tasks']}, 完成 {progress['summary']['completed_tasks']}")
        
        # 获取系统健康状态
        health = monitor.get_system_health()
        if health:
            print(f"系统健康状态: {health['status']}")
        
        print("✅ 系统监控器测试通过")
        
    except Exception as e:
        print(f"❌ 系统监控器测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("开始测试高级功能模块...")
    
    test_incremental_updater()
    test_data_validator()
    test_system_monitor()
    
    print("\n🎉 所有测试完成！")


if __name__ == '__main__':
    main()
