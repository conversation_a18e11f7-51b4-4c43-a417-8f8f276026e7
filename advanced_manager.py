#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
高级管理工具
整合增量更新、数据验证、监控报告等高级功能
"""

import argparse
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config_manager import ConfigManager
from src.core.database import DatabaseManager
from src.core.logger import setup_logger
from src.storage.data_storage import DataStorage
from src.utils.incremental_updater import IncrementalUpdater
from src.utils.data_validator import DataValidator
from src.utils.monitor import SystemMonitor


class AdvancedManager:
    """高级管理器"""
    
    def __init__(self):
        self.config = ConfigManager()
        self.logger = setup_logger(self.config)
        
        # 初始化核心组件
        self.db_manager = DatabaseManager(self.config)
        self.db_manager.initialize()
        
        self.storage = DataStorage(self.db_manager, self.config)
        self.updater = IncrementalUpdater(self.db_manager, self.storage)
        self.validator = DataValidator(self.db_manager, self.storage)
        self.monitor = SystemMonitor(self.db_manager)
    
    def check_incremental_updates(self):
        """检查增量更新"""
        print("=== 检查增量更新需求 ===")
        
        tables_to_update = self.updater.get_tables_need_update()
        
        if not tables_to_update:
            print("✅ 所有表都是最新的，无需更新")
            return
        
        print(f"📋 需要更新的表: {', '.join(tables_to_update)}")
        
        for table_name in tables_to_update:
            print(f"\n🔄 检查表 {table_name} 的增量更新范围...")
            
            date_range = self.updater.get_incremental_date_range(table_name)
            if date_range:
                start_date, end_date = date_range
                print(f"   增量更新范围: {start_date} - {end_date}")
            else:
                print(f"   表 {table_name} 无需增量更新或不支持增量更新")
    
    def validate_data(self, table_name=None):
        """验证数据"""
        print("=== 数据验证 ===")
        
        if table_name:
            print(f"🔍 验证表: {table_name}")
            result = self.validator.validate_table(table_name)
            self._print_validation_result(table_name, result)
        else:
            print("🔍 验证所有表...")
            results = self.validator.validate_all_tables()
            
            print(f"\n📊 验证摘要:")
            print(f"   总表数: {results['total_tables']}")
            print(f"   通过验证: {results['passed_tables']}")
            print(f"   验证失败: {results['failed_tables']}")
            print(f"   总问题数: {results['total_issues']}")
            
            print(f"\n📋 详细结果:")
            for table_name, result in results['results'].items():
                self._print_validation_result(table_name, result)
    
    def show_system_status(self):
        """显示系统状态"""
        print("=== 系统状态 ===")
        
        # 下载进度
        progress = self.monitor.get_download_progress()
        if progress:
            print(f"\n📈 下载进度:")
            summary = progress['summary']
            print(f"   总任务数: {summary['total_tasks']}")
            print(f"   已完成: {summary['completed_tasks']}")
            print(f"   完成率: {summary['completion_rate']:.1%}")
            print(f"   总记录数: {summary['total_records']:,}")
            
            print(f"\n📊 各类型进度:")
            for data_type, stats in progress['progress_by_type'].items():
                if isinstance(stats, dict):
                    total = sum(stats.values())
                    completed = stats.get('completed', 0)
                    print(f"   {data_type}: {completed}/{total} ({completed/total:.1%} 完成)" if total > 0 else f"   {data_type}: 无任务")
        
        # 系统健康状态
        health = self.monitor.get_system_health()
        if health:
            print(f"\n🏥 系统健康状态:")
            print(f"   总体状态: {health['status']} (评分: {health.get('overall_score', 0):.1%})")
            print(f"   数据库: {health['database']['status']}")
            print(f"   数据完整性: {health['data_integrity']['status']}")
            if health['recent_errors']:
                print(f"   最近错误: {len(health['recent_errors'])} 个")
    
    def show_performance_metrics(self):
        """显示性能指标"""
        print("=== 性能指标 ===")
        
        metrics = self.monitor.get_performance_metrics()
        if metrics:
            print(f"\n⚡ 下载性能:")
            print(f"   平均任务耗时: {metrics['download_performance']['avg_task_duration_minutes']:.1f} 分钟")
            
            print(f"\n🌐 API性能:")
            api_perf = metrics['api_performance']
            print(f"   总调用次数: {api_perf['total_calls']}")
            print(f"   成功率: {api_perf['success_rate']:.1f}%")
            print(f"   平均响应时间: {api_perf['avg_response_time_ms']:.1f} ms")
            
            print(f"\n❌ 错误指标:")
            print(f"   错误率: {metrics['error_metrics']['error_rate_percent']:.1f}%")
    
    def generate_report(self, report_type='daily'):
        """生成报告"""
        print(f"=== 生成{report_type}报告 ===")
        
        if report_type == 'daily':
            report = self.monitor.generate_daily_report()
            if report:
                print(f"✅ 日报生成完成")
                print(f"   报告日期: {report['date']}")
                print(f"   下载统计: {len(report['download_statistics'])} 个数据类型")
                print(f"   错误统计: {len(report['error_statistics'])} 种错误类型")
            else:
                print("❌ 日报生成失败")
    
    def repair_data(self, table_name):
        """修复数据"""
        print(f"=== 修复表 {table_name} ===")
        
        # 先验证数据找出问题
        result = self.validator.validate_table(table_name)
        
        if result.get('status') == 'passed':
            print(f"✅ 表 {table_name} 数据正常，无需修复")
            return
        
        issues = result.get('issues', [])
        if not issues:
            print(f"❓ 表 {table_name} 没有发现具体问题")
            return
        
        print(f"🔧 发现 {len(issues)} 个问题，开始修复...")
        for issue in issues:
            print(f"   - {issue}")
        
        success = self.validator.repair_data_issues(table_name, issues)
        if success:
            print(f"✅ 表 {table_name} 修复完成")
        else:
            print(f"❌ 表 {table_name} 修复失败")
    
    def _print_validation_result(self, table_name, result):
        """打印验证结果"""
        status = result.get('status', 'unknown')
        if status == 'passed':
            print(f"   ✅ {table_name}: 验证通过 ({result.get('record_count', 0):,} 条记录)")
        elif status == 'failed':
            issues = result.get('issues', [])
            print(f"   ❌ {table_name}: 验证失败 ({len(issues)} 个问题)")
            for issue in issues:
                print(f"      - {issue}")
        elif status == 'skipped':
            print(f"   ⏭️  {table_name}: 跳过验证 ({result.get('reason', 'unknown')})")
        else:
            print(f"   ❓ {table_name}: 验证状态未知")


def main():
    parser = argparse.ArgumentParser(description='tushare数据下载系统 - 高级管理工具')
    
    parser.add_argument('--check-updates', action='store_true',
                       help='检查增量更新需求')
    
    parser.add_argument('--validate', metavar='TABLE',
                       help='验证数据完整性 (指定表名或留空验证所有表)')
    
    parser.add_argument('--validate-all', action='store_true',
                       help='验证所有表的数据完整性')
    
    parser.add_argument('--status', action='store_true',
                       help='显示系统状态')
    
    parser.add_argument('--performance', action='store_true',
                       help='显示性能指标')
    
    parser.add_argument('--report', choices=['daily'],
                       help='生成报告')
    
    parser.add_argument('--repair', metavar='TABLE',
                       help='修复指定表的数据问题')
    
    args = parser.parse_args()
    
    if not any(vars(args).values()):
        parser.print_help()
        return
    
    try:
        manager = AdvancedManager()
        
        if args.check_updates:
            manager.check_incremental_updates()
        
        if args.validate:
            manager.validate_data(args.validate)
        
        if args.validate_all:
            manager.validate_data()
        
        if args.status:
            manager.show_system_status()
        
        if args.performance:
            manager.show_performance_metrics()
        
        if args.report:
            manager.generate_report(args.report)
        
        if args.repair:
            manager.repair_data(args.repair)
            
    except KeyboardInterrupt:
        print("\n⚠️  操作被用户中断")
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
