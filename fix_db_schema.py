#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复数据库表结构
"""

import sqlite3

def fix_database_schema():
    """修复数据库表结构"""
    conn = sqlite3.connect('data/tushare_data.db')
    cursor = conn.cursor()
    
    try:
        # 检查data_status表结构
        cursor.execute('PRAGMA table_info(data_status)')
        columns = cursor.fetchall()
        print('data_status表结构:')
        for col in columns:
            print(f'  {col[1]} {col[2]}')
        
        # 检查是否已有validation_status列
        column_names = [col[1] for col in columns]
        if 'validation_status' not in column_names:
            # 添加validation_status列
            cursor.execute('ALTER TABLE data_status ADD COLUMN validation_status TEXT DEFAULT "unknown"')
            print('\n已添加validation_status列')
        else:
            print('\nvalidation_status列已存在')
        
        conn.commit()
        print('数据库表结构修复完成')
        
    except Exception as e:
        print(f'修复失败: {e}')
    finally:
        conn.close()

if __name__ == '__main__':
    fix_database_schema()
