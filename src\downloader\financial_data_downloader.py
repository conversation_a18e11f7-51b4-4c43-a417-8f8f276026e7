#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务数据下载器
负责下载利润表、资产负债表、现金流量表等财务数据
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from ..core.logger import LoggerMixin


class FinancialDataDownloader(LoggerMixin):
    """财务数据下载器"""
    
    def __init__(self, api, storage, progress_tracker, config):
        """初始化财务数据下载器"""
        self.api = api
        self.storage = storage
        self.progress_tracker = progress_tracker
        self.config = config
        
        # 获取下载配置
        self.download_config = config.get_download_config()
        self.batch_size = self.download_config.get('batch_size', 1000)
        
        self.log_info("财务数据下载器初始化完成")
    
    def download_income_data(self, stock_list: List[str], 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None):
        """下载利润表数据"""
        try:
            self.log_info("开始下载利润表数据")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='income',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载利润表")
            
            total_records = 0
            
            # 按股票代码下载
            for i, ts_code in enumerate(stock_list):
                try:
                    self.log_debug(f"下载股票 {ts_code} 利润表")
                    
                    data = self.api.get_income(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('income', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('income', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载股票 {ts_code} 利润表失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"利润表数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载利润表数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_balance_sheet(self, stock_list: List[str], 
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None):
        """下载资产负债表数据"""
        try:
            self.log_info("开始下载资产负债表数据")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='balancesheet',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载资产负债表")
            
            total_records = 0
            
            # 按股票代码下载
            for i, ts_code in enumerate(stock_list):
                try:
                    self.log_debug(f"下载股票 {ts_code} 资产负债表")
                    
                    data = self.api.get_balancesheet(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('balancesheet', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('balancesheet', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载股票 {ts_code} 资产负债表失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"资产负债表数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载资产负债表数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_cashflow_data(self, stock_list: List[str], 
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None):
        """下载现金流量表数据"""
        try:
            self.log_info("开始下载现金流量表数据")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='cashflow',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载现金流量表")
            
            total_records = 0
            
            # 按股票代码下载
            for i, ts_code in enumerate(stock_list):
                try:
                    self.log_debug(f"下载股票 {ts_code} 现金流量表")
                    
                    data = self.api.get_cashflow(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('cashflow', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('cashflow', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载股票 {ts_code} 现金流量表失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"现金流量表数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载现金流量表数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_financial_indicators(self, stock_list: List[str], 
                                     start_date: Optional[str] = None,
                                     end_date: Optional[str] = None):
        """下载财务指标数据"""
        try:
            self.log_info("开始下载财务指标数据")
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='fina_indicator',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载财务指标")
            
            total_records = 0
            
            # 按股票代码下载
            for i, ts_code in enumerate(stock_list):
                try:
                    self.log_debug(f"下载股票 {ts_code} 财务指标")
                    
                    data = self.api.get_fina_indicator(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('fina_indicator', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('fina_indicator', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载股票 {ts_code} 财务指标失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"财务指标数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载财务指标数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_forecast_data(self, stock_list: List[str], 
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None):
        """下载业绩预告数据"""
        try:
            self.log_info("开始下载业绩预告数据")
            
            # 下载业绩预告
            self._download_forecast(stock_list, start_date, end_date)
            
            # 下载业绩快报
            self._download_express(stock_list, start_date, end_date)
            
        except Exception as e:
            self.log_error("下载业绩预告数据失败", e)
            raise
    
    def _download_forecast(self, stock_list: List[str], start_date: Optional[str], end_date: Optional[str]):
        """下载业绩预告"""
        # 实现类似上面的下载逻辑
        pass
    
    def download_express_data(self, stock_list: List[str],
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None):
        """下载业绩快报数据"""
        try:
            self.log_info("开始下载业绩快报数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='express'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_express(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('express', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 业绩快报数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"业绩快报数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载业绩快报数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_dividend_data(self, stock_list: List[str],
                              start_date: Optional[str] = None,
                              end_date: Optional[str] = None):
        """下载分红送股数据"""
        try:
            self.log_info("开始下载分红送股数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='dividend'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_dividend(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('dividend', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 分红送股数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"分红送股数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载分红送股数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_fina_audit(self, stock_list: List[str],
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None):
        """下载财务审计意见数据"""
        try:
            self.log_info("开始下载财务审计意见数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='fina_audit'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_fina_audit(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('fina_audit', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 财务审计意见数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"财务审计意见数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载财务审计意见数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_fina_mainbz(self, stock_list: List[str],
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None):
        """下载主营业务构成数据"""
        try:
            self.log_info("开始下载主营业务构成数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='fina_mainbz'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_fina_mainbz(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('fina_mainbz', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 主营业务构成数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"主营业务构成数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载主营业务构成数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_fina_indicator(self, stock_list: List[str],
                               start_date: Optional[str] = None,
                               end_date: Optional[str] = None):
        """下载财务指标数据"""
        try:
            self.log_info("开始下载财务指标数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='financial',
                table_name='fina_indicator'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_fina_indicator(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('fina_indicator', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 财务指标数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"财务指标数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载财务指标数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def validate_data(self):
        """验证财务数据完整性"""
        try:
            self.log_info("开始验证财务数据完整性")
            
            # 验证利润表
            income_count = self.storage.get_data_count('income')
            self.log_info(f"利润表: {income_count} 条记录")
            
            # 验证资产负债表
            balance_count = self.storage.get_data_count('balancesheet')
            self.log_info(f"资产负债表: {balance_count} 条记录")
            
            # 验证现金流量表
            cashflow_count = self.storage.get_data_count('cashflow')
            self.log_info(f"现金流量表: {cashflow_count} 条记录")
            
            self.log_info("财务数据验证完成")
            
        except Exception as e:
            self.log_error("验证财务数据失败", e)
            raise
    
    def repair_data(self):
        """修复财务数据"""
        try:
            self.log_info("开始修复财务数据")
            
            # 获取股票列表
            stock_list = self.storage.get_stock_list()
            if not stock_list:
                self.log_warning("未找到股票列表，无法修复财务数据")
                return
            
            # 检查并修复利润表
            if self.storage.get_data_count('income') == 0:
                self.log_info("重新下载利润表数据")
                self.download_income_data(stock_list)
            
            self.log_info("财务数据修复完成")
            
        except Exception as e:
            self.log_error("修复财务数据失败", e)
            raise
