#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据库连接和表创建
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.database import DatabaseManager
from sqlalchemy import text

def test_database():
    """测试数据库连接和表创建"""
    try:
        # 初始化配置
        config = ConfigManager('config.yaml')
        
        # 初始化数据库管理器
        db_manager = DatabaseManager(config)

        # 初始化数据库连接和表
        db_manager.initialize()
        
        # 测试连接
        with db_manager.get_session() as session:
            result = session.execute(text("SELECT name FROM sqlite_master WHERE type='table';"))
            tables = [row[0] for row in result.fetchall()]
            print(f"数据库中的表: {tables}")
        
        print("数据库测试成功！")
        
    except Exception as e:
        print(f"数据库测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database()
