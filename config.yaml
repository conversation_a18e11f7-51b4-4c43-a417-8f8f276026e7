# tushare数据下载系统配置文件

# tushare配置
tushare:
  token: "2876ea85cb005fb5fa17c809a98174f2d5aae8b1f830110a5ead6211"
  api_timeout: 30  # API超时时间（秒）
  retry_times: 3   # 重试次数
  retry_delay: 1   # 重试延迟（秒）
  rate_limit: 200  # 每分钟最大请求数

# 数据库配置
database:
  type: "sqlite"  # 支持 sqlite, postgresql
  sqlite:
    path: "data/tushare_data.db"
  postgresql:
    host: "localhost"
    port: 5432
    database: "tushare"
    username: "postgres"
    password: "password"

# 下载配置
download:
  batch_size: 1000      # 批量下载大小
  max_workers: 3        # 最大并发数
  chunk_size: 5000      # 单次请求数据量
  data_start_date: "20000101"  # 数据开始日期
  
# 存储配置
storage:
  data_dir: "data"           # 数据存储目录
  backup_dir: "backup"       # 备份目录
  log_dir: "logs"           # 日志目录
  enable_backup: true        # 是否启用备份
  backup_interval: 7         # 备份间隔（天）

# 日志配置
logging:
  level: "INFO"              # 日志级别: DEBUG, INFO, WARNING, ERROR
  max_file_size: "10MB"      # 单个日志文件最大大小
  backup_count: 5            # 保留日志文件数量
  console_output: true       # 是否输出到控制台

# 监控配置
monitoring:
  enable_progress_bar: true  # 是否显示进度条
  status_update_interval: 10 # 状态更新间隔（秒）
  enable_email_notify: false # 是否启用邮件通知
  
# 数据验证配置
validation:
  enable_data_check: true    # 是否启用数据完整性检查
  check_interval: 24         # 检查间隔（小时）
  auto_repair: true          # 是否自动修复数据
