#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
行情数据下载器
负责下载日线、周线、月线等行情数据
"""

import time
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any

from ..core.logger import LoggerMixin


class MarketDataDownloader(LoggerMixin):
    """行情数据下载器"""
    
    def __init__(self, api, storage, progress_tracker, config):
        """初始化行情数据下载器"""
        self.api = api
        self.storage = storage
        self.progress_tracker = progress_tracker
        self.config = config
        
        # 获取下载配置
        self.download_config = config.get_download_config()
        self.batch_size = self.download_config.get('batch_size', 1000)
        self.chunk_size = self.download_config.get('chunk_size', 5000)
        
        self.log_info("行情数据下载器初始化完成")
    
    def download_daily_data(self, stock_list: List[str], 
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None):
        """下载日线行情数据"""
        try:
            self.log_info("开始下载日线行情数据")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='market',
                table_name='daily',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载日线数据")
            
            total_records = 0
            
            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]
                
                try:
                    # 按股票代码下载
                    for j, ts_code in enumerate(batch_stocks):
                        try:
                            self.log_debug(f"下载股票 {ts_code} 日线数据")
                            
                            data = self.api.get_daily(
                                ts_code=ts_code,
                                start_date=start_date,
                                end_date=end_date
                            )
                            
                            if not data.empty:
                                # 保存数据
                                if i == 0 and j == 0:
                                    record_count = self.storage.save_data('daily', data, if_exists='replace')
                                else:
                                    record_count = self.storage.save_data('daily', data, if_exists='append')
                                
                                total_records += record_count
                            
                            # 更新进度
                            current_progress = i + j + 1
                            self.progress_tracker.update_progress(task_id, current_progress)
                            
                            # 避免API调用过于频繁
                            time.sleep(0.1)
                            
                        except Exception as e:
                            self.log_warning(f"下载股票 {ts_code} 日线数据失败: {e}")
                            continue
                    
                except Exception as e:
                    self.log_warning(f"下载批次失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"日线行情数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载日线行情数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_daily_basic(self, stock_list: List[str], 
                            start_date: Optional[str] = None,
                            end_date: Optional[str] = None):
        """下载每日指标数据"""
        try:
            self.log_info("开始下载每日指标数据")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='market',
                table_name='daily_basic',
                start_date=start_date,
                end_date=end_date,
                total_records=1  # 只需要一次API调用
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, 1, "下载每日指标")

            total_records = 0

            try:
                self.log_info(f"下载每日指标数据: {start_date} 到 {end_date}")

                # 一次性下载指定日期范围的所有数据
                data = self.api.get_daily_basic(
                    start_date=start_date,
                    end_date=end_date
                )

                if not data.empty:
                    # 保存数据
                    record_count = self.storage.save_data('daily_basic', data, if_exists='replace')
                    total_records = record_count

                    self.log_info(f"每日指标数据下载完成，共 {record_count} 条记录")
                else:
                    self.log_warning("每日指标数据为空")

                # 更新进度
                self.progress_tracker.update_progress(task_id, 1)

            except Exception as e:
                self.log_error(f"下载每日指标数据失败: {e}")
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
                raise
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"每日指标数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载每日指标数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_adj_factor(self, stock_list: List[str], 
                           start_date: Optional[str] = None,
                           end_date: Optional[str] = None):
        """下载复权因子数据"""
        try:
            self.log_info("开始下载复权因子数据")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='market',
                table_name='adj_factor',
                start_date=start_date,
                end_date=end_date,
                total_records=len(stock_list)
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 创建进度条
            self.progress_tracker.create_progress_bar(task_id, len(stock_list), "下载复权因子")
            
            total_records = 0
            
            # 按股票代码下载
            for i, ts_code in enumerate(stock_list):
                try:
                    self.log_debug(f"下载股票 {ts_code} 复权因子")
                    
                    data = self.api.get_adj_factor(
                        ts_code=ts_code,
                        start_date=start_date,
                        end_date=end_date
                    )
                    
                    if not data.empty:
                        # 保存数据
                        if i == 0:
                            record_count = self.storage.save_data('adj_factor', data, if_exists='replace')
                        else:
                            record_count = self.storage.save_data('adj_factor', data, if_exists='append')
                        
                        total_records += record_count
                    
                    # 更新进度
                    self.progress_tracker.update_progress(task_id, i + 1)
                    
                    # 避免API调用过于频繁
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.log_warning(f"下载股票 {ts_code} 复权因子失败: {e}")
                    continue
            
            # 关闭进度条
            self.progress_tracker.close_progress_bar(task_id)
            
            # 更新任务状态
            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"复权因子数据下载完成，共 {total_records} 条记录")
            
        except Exception as e:
            self.log_error("下载复权因子数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def download_suspend_data(self, start_date: Optional[str] = None,
                             end_date: Optional[str] = None):
        """下载停牌信息"""
        try:
            self.log_info("开始下载停牌信息")
            
            # 设置默认日期范围
            if not start_date:
                start_date = self.download_config.get('data_start_date', '20000101')
            if not end_date:
                end_date = datetime.now().strftime('%Y%m%d')
            
            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='market',
                table_name='suspend_d',
                start_date=start_date,
                end_date=end_date
            )
            
            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')
            
            # 下载数据
            data = self.api.get_suspend_d(
                suspend_date=start_date,
                resume_date=end_date
            )
            
            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('suspend_d', data, if_exists='replace')
                
                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')
                
                self.log_info(f"停牌信息下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'completed')
                self.log_info("停牌信息数据为空")
            
        except Exception as e:
            self.log_error("下载停牌信息失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise
    
    def validate_data(self):
        """验证行情数据完整性"""
        try:
            self.log_info("开始验证行情数据完整性")
            
            # 验证日线数据
            daily_count = self.storage.get_data_count('daily')
            self.log_info(f"日线数据: {daily_count} 条记录")
            
            # 验证每日指标
            daily_basic_count = self.storage.get_data_count('daily_basic')
            self.log_info(f"每日指标: {daily_basic_count} 条记录")
            
            # 验证复权因子
            adj_factor_count = self.storage.get_data_count('adj_factor')
            self.log_info(f"复权因子: {adj_factor_count} 条记录")
            
            self.log_info("行情数据验证完成")
            
        except Exception as e:
            self.log_error("验证行情数据失败", e)
            raise
    
    def download_moneyflow(self, stock_list: List[str],
                          start_date: Optional[str] = None,
                          end_date: Optional[str] = None):
        """下载资金流向数据"""
        try:
            self.log_info("开始下载资金流向数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='daily',
                table_name='moneyflow'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            total_records = 0

            # 分批下载
            for i in range(0, len(stock_list), self.batch_size):
                batch_stocks = stock_list[i:i + self.batch_size]

                for ts_code in batch_stocks:
                    try:
                        # 下载数据
                        data = self.api.get_moneyflow(
                            ts_code=ts_code,
                            start_date=start_date,
                            end_date=end_date
                        )

                        if not data.empty:
                            # 保存数据
                            record_count = self.storage.save_data('moneyflow', data, if_exists='append')
                            total_records += record_count

                            # 更新进度
                            self.progress_tracker.update_progress(task_id, total_records)

                    except Exception as e:
                        self.log_warning(f"下载 {ts_code} 资金流向数据失败: {str(e)}")
                        continue

            self.progress_tracker.update_task_status(task_id, 'completed')
            self.log_info(f"资金流向数据下载完成，共 {total_records} 条记录")

        except Exception as e:
            self.log_error("下载资金流向数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise

    def download_stk_limit(self, start_date: Optional[str] = None,
                          end_date: Optional[str] = None):
        """下载涨跌停价格数据"""
        try:
            self.log_info("开始下载涨跌停价格数据")

            # 创建下载任务
            task_id = self.progress_tracker.create_task(
                data_type='daily',
                table_name='stk_limit'
            )

            # 更新任务状态为运行中
            self.progress_tracker.update_task_status(task_id, 'running')

            # 下载数据
            data = self.api.get_stk_limit(
                start_date=start_date,
                end_date=end_date
            )

            if not data.empty:
                # 保存数据
                record_count = self.storage.save_data('stk_limit', data, if_exists='replace')

                # 更新进度
                self.progress_tracker.update_progress(task_id, record_count)
                self.progress_tracker.update_task_status(task_id, 'completed')

                self.log_info(f"涨跌停价格数据下载完成，共 {record_count} 条记录")
            else:
                self.progress_tracker.update_task_status(task_id, 'failed', '获取数据为空')
                self.log_warning("涨跌停价格数据为空")

        except Exception as e:
            self.log_error("下载涨跌停价格数据失败", e)
            if 'task_id' in locals():
                self.progress_tracker.update_task_status(task_id, 'failed', str(e))
            raise



    def repair_data(self):
        """修复行情数据"""
        try:
            self.log_info("开始修复行情数据")

            # 获取股票列表
            stock_list = self.storage.get_stock_list()
            if not stock_list:
                self.log_warning("未找到股票列表，无法修复行情数据")
                return

            # 检查并修复日线数据
            if self.storage.get_data_count('daily') == 0:
                self.log_info("重新下载日线数据")
                self.download_daily_data(stock_list)

            self.log_info("行情数据修复完成")

        except Exception as e:
            self.log_error("修复行情数据失败", e)
            raise
