#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取本机IP地址
"""

import socket
import subprocess
import sys

def get_local_ip():
    """获取本机IP地址"""
    try:
        # 方法1: 连接外部地址获取本机IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return None

def get_ip_from_ipconfig():
    """从ipconfig获取IP地址"""
    try:
        result = subprocess.run(['ipconfig'], capture_output=True, text=True, encoding='gbk')
        lines = result.stdout.split('\n')
        
        for line in lines:
            if 'IPv4' in line and '192.168' in line:
                # 提取IP地址
                ip = line.split(':')[-1].strip()
                return ip
        return None
    except:
        return None

def main():
    print("🔍 正在获取你的电脑IP地址...")
    print("="*50)
    
    # 尝试多种方法获取IP
    ip1 = get_local_ip()
    ip2 = get_ip_from_ipconfig()
    
    print("📍 检测到的IP地址:")
    if ip1:
        print(f"   方法1: {ip1}")
    if ip2:
        print(f"   方法2: {ip2}")
    
    # 确定最终IP
    final_ip = ip1 or ip2 or "无法获取"
    
    print("\n" + "="*50)
    print("📱 手机访问地址:")
    print(f"   电脑版: http://{final_ip}:5000")
    print(f"   手机版: http://{final_ip}:8080")
    print("\n📋 使用步骤:")
    print("1. 确保手机和电脑连接同一个WiFi")
    print("2. 在手机浏览器输入上面的地址")
    print("3. 如果无法访问，请检查防火墙设置")
    print("\n🔧 如果还是无法访问:")
    print("1. 关闭Windows防火墙")
    print("2. 或者在防火墙中允许Python程序")
    print("3. 确认路由器没有设备隔离")

if __name__ == '__main__':
    main()
