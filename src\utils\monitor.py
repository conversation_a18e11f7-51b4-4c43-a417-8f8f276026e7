#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
监控和报告模块
提供下载进度监控、数据统计报告等功能
"""

import pandas as pd
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from src.core.logger import LoggerMixin
from src.core.database import DatabaseManager


class SystemMonitor(LoggerMixin):
    """系统监控器"""
    
    def __init__(self, db_manager: DatabaseManager):
        super().__init__()
        self.db_manager = db_manager
    
    def get_download_progress(self) -> Dict[str, Any]:
        """获取下载进度报告"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # 获取下载任务进度
                progress_data = session.execute(text("""
                    SELECT data_type, status, COUNT(*) as count,
                           MIN(created_at) as start_time,
                           MAX(updated_at) as last_update
                    FROM download_progress 
                    GROUP BY data_type, status
                    ORDER BY data_type, status
                """)).fetchall()
                
                # 获取数据表统计
                table_stats = session.execute(text("""
                    SELECT table_name, record_count, is_complete, last_updated
                    FROM data_status
                    WHERE date IS NULL
                    ORDER BY table_name
                """)).fetchall()
                
                # 处理进度数据
                progress_summary = {}
                for row in progress_data:
                    data_type = row[0]
                    if data_type not in progress_summary:
                        progress_summary[data_type] = {
                            'pending': 0, 'running': 0, 'completed': 0, 'failed': 0,
                            'start_time': None, 'last_update': None
                        }
                    
                    progress_summary[data_type][row[1]] = row[2]
                    if not progress_summary[data_type]['start_time'] or row[3] < progress_summary[data_type]['start_time']:
                        progress_summary[data_type]['start_time'] = row[3]
                    if not progress_summary[data_type]['last_update'] or row[4] > progress_summary[data_type]['last_update']:
                        progress_summary[data_type]['last_update'] = row[4]
                
                # 处理表统计数据
                table_summary = {}
                for row in table_stats:
                    table_summary[row[0]] = {
                        'record_count': row[1] or 0,
                        'is_complete': bool(row[2]),
                        'last_updated': row[3],
                        'validation_status': 'unknown'
                    }
                
                # 计算总体统计
                total_tasks = 0
                total_completed = 0
                for stats in progress_summary.values():
                    if isinstance(stats, dict):
                        # 只计算数字值，跳过字符串值
                        for key, value in stats.items():
                            if isinstance(value, int):
                                total_tasks += value
                                if key == 'completed':
                                    total_completed += value
                total_records = sum(stats['record_count'] for stats in table_summary.values())
                
                return {
                    'summary': {
                        'total_tasks': total_tasks,
                        'completed_tasks': total_completed,
                        'completion_rate': total_completed / total_tasks if total_tasks > 0 else 0,
                        'total_records': total_records,
                        'generated_at': datetime.now()
                    },
                    'progress_by_type': progress_summary,
                    'table_statistics': table_summary
                }
                
        except Exception as e:
            self.log_error("获取下载进度失败", e)
            return {}
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        try:
            health_status = {
                'database': self._check_database_health(),
                'data_integrity': self._check_data_integrity(),
                'recent_errors': self._get_recent_errors(),
                'disk_usage': self._check_disk_usage(),
                'api_status': self._check_api_status(),
                'checked_at': datetime.now()
            }
            
            # 计算总体健康分数
            scores = []
            if health_status['database']['status'] == 'healthy':
                scores.append(1)
            else:
                scores.append(0)
            
            if health_status['data_integrity'].get('issues_count', 0) == 0:
                scores.append(1)
            else:
                scores.append(max(0, 1 - health_status['data_integrity'].get('issues_count', 0) / 10))
            
            if len(health_status['recent_errors']) == 0:
                scores.append(1)
            else:
                scores.append(max(0, 1 - len(health_status['recent_errors']) / 20))
            
            health_status['overall_score'] = sum(scores) / len(scores) if scores else 0
            health_status['status'] = 'healthy' if health_status['overall_score'] > 0.8 else 'warning' if health_status['overall_score'] > 0.5 else 'critical'
            
            return health_status
            
        except Exception as e:
            self.log_error("获取系统健康状态失败", e)
            return {'status': 'error', 'message': str(e)}
    
    def generate_daily_report(self) -> Dict[str, Any]:
        """生成日报"""
        try:
            today = datetime.now().date()
            yesterday = today - timedelta(days=1)
            
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # 获取昨日下载统计
                daily_stats = session.execute(text("""
                    SELECT data_type, status, COUNT(*) as count
                    FROM download_progress 
                    WHERE DATE(created_at) = :yesterday
                    GROUP BY data_type, status
                """), {'yesterday': yesterday}).fetchall()
                
                # 获取昨日错误统计
                error_stats = session.execute(text("""
                    SELECT error_type, COUNT(*) as count
                    FROM error_logs 
                    WHERE DATE(created_at) = :yesterday
                    GROUP BY error_type
                """), {'yesterday': yesterday}).fetchall()
                
                # 获取数据增长统计
                growth_stats = self._calculate_data_growth(yesterday)
                
                report = {
                    'date': yesterday,
                    'download_statistics': {row[0]: {row[1]: row[2]} for row in daily_stats},
                    'error_statistics': {row[0]: row[1] for row in error_stats},
                    'data_growth': growth_stats,
                    'system_health': self.get_system_health(),
                    'generated_at': datetime.now()
                }
                
                # 保存报告
                self._save_report(report, 'daily')
                
                return report
                
        except Exception as e:
            self.log_error("生成日报失败", e)
            return {}
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # 计算下载速度
                download_speed = session.execute(text("""
                    SELECT AVG(
                        CASE 
                            WHEN completed_at IS NOT NULL AND created_at IS NOT NULL 
                            THEN (julianday(completed_at) - julianday(created_at)) * 24 * 60
                            ELSE NULL 
                        END
                    ) as avg_duration_minutes
                    FROM download_progress 
                    WHERE status = 'completed' AND completed_at IS NOT NULL
                """)).fetchone()
                
                # 计算API调用统计
                api_stats = session.execute(text("""
                    SELECT 
                        COUNT(*) as total_calls,
                        COUNT(CASE WHEN status = 'success' THEN 1 END) as successful_calls,
                        AVG(response_time) as avg_response_time
                    FROM api_calls 
                    WHERE DATE(created_at) >= DATE('now', '-7 days')
                """)).fetchone()
                
                # 计算错误率
                error_rate = session.execute(text("""
                    SELECT 
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) * 100.0 / COUNT(*) as error_rate
                    FROM download_progress 
                    WHERE DATE(created_at) >= DATE('now', '-7 days')
                """)).fetchone()
                
                return {
                    'download_performance': {
                        'avg_task_duration_minutes': download_speed[0] if download_speed and download_speed[0] else 0
                    },
                    'api_performance': {
                        'total_calls': api_stats[0] if api_stats else 0,
                        'success_rate': (api_stats[1] / api_stats[0] * 100) if api_stats and api_stats[0] > 0 else 0,
                        'avg_response_time_ms': api_stats[2] if api_stats else 0
                    },
                    'error_metrics': {
                        'error_rate_percent': error_rate[0] if error_rate and error_rate[0] else 0
                    },
                    'calculated_at': datetime.now()
                }
                
        except Exception as e:
            self.log_error("获取性能指标失败", e)
            return {}
    
    def _check_database_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # 检查数据库连接
                session.execute(text("SELECT 1")).fetchone()
                
                # 检查表是否存在
                tables = session.execute(text("""
                    SELECT name FROM sqlite_master WHERE type='table'
                """)).fetchall()
                
                return {
                    'status': 'healthy',
                    'table_count': len(tables),
                    'tables': [row[0] for row in tables]
                }
                
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def _check_data_integrity(self) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text

                # 简单检查：统计记录数为0的表
                empty_tables = session.execute(text("""
                    SELECT table_name
                    FROM data_status
                    WHERE record_count = 0 OR record_count IS NULL
                """)).fetchall()

                return {
                    'status': 'good' if not empty_tables else 'issues_found',
                    'issues_count': len(empty_tables),
                    'empty_tables': [row[0] for row in empty_tables]
                }

        except Exception as e:
            return {
                'status': 'unknown',
                'issues_count': 0,
                'error': str(e)
            }
    
    def _get_recent_errors(self) -> List[Dict[str, Any]]:
        """获取最近的错误"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                errors = session.execute(text("""
                    SELECT error_type, error_message, created_at
                    FROM error_logs 
                    WHERE created_at >= datetime('now', '-24 hours')
                    ORDER BY created_at DESC
                    LIMIT 10
                """)).fetchall()
                
                return [
                    {
                        'type': row[0],
                        'message': row[1],
                        'time': row[2]
                    }
                    for row in errors
                ]
                
        except Exception as e:
            self.log_error("获取最近错误失败", e)
            return []
    
    def _check_disk_usage(self) -> Dict[str, Any]:
        """检查磁盘使用情况"""
        try:
            import os
            import shutil
            
            # 获取数据目录大小
            data_dir = "data"
            if os.path.exists(data_dir):
                total_size = sum(
                    os.path.getsize(os.path.join(dirpath, filename))
                    for dirpath, dirnames, filenames in os.walk(data_dir)
                    for filename in filenames
                )
                
                # 获取可用空间
                free_space = shutil.disk_usage(data_dir).free
                
                return {
                    'data_size_mb': total_size / (1024 * 1024),
                    'free_space_gb': free_space / (1024 * 1024 * 1024),
                    'status': 'ok' if free_space > 1024 * 1024 * 1024 else 'low_space'  # 1GB阈值
                }
            else:
                return {'status': 'data_dir_not_found'}
                
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def _check_api_status(self) -> Dict[str, Any]:
        """检查API状态"""
        try:
            with self.db_manager.get_session() as session:
                from sqlalchemy import text
                
                # 获取最近的API调用状态
                recent_calls = session.execute(text("""
                    SELECT status, COUNT(*) as count
                    FROM api_calls 
                    WHERE created_at >= datetime('now', '-1 hour')
                    GROUP BY status
                """)).fetchall()
                
                total_calls = sum(row[1] for row in recent_calls)
                success_calls = sum(row[1] for row in recent_calls if row[0] == 'success')
                
                success_rate = (success_calls / total_calls * 100) if total_calls > 0 else 0
                
                return {
                    'status': 'healthy' if success_rate > 90 else 'degraded' if success_rate > 70 else 'unhealthy',
                    'success_rate': success_rate,
                    'total_calls_last_hour': total_calls
                }
                
        except Exception as e:
            return {'status': 'unknown', 'error': str(e)}
    
    def _calculate_data_growth(self, date) -> Dict[str, Any]:
        """计算数据增长"""
        # 这里可以实现数据增长计算逻辑
        return {
            'new_records': 0,
            'updated_records': 0,
            'deleted_records': 0
        }
    
    def _save_report(self, report: Dict[str, Any], report_type: str):
        """保存报告"""
        try:
            report_file = f"reports/{report_type}_report_{datetime.now().strftime('%Y%m%d')}.json"
            os.makedirs("reports", exist_ok=True)
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            
            self.log_info(f"报告已保存: {report_file}")
            
        except Exception as e:
            self.log_error(f"保存报告失败", e)
