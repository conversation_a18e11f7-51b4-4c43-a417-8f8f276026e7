#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度跟踪器
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.database import DatabaseManager
from src.utils.progress_tracker import ProgressTracker

def test_progress_tracker():
    """测试进度跟踪器"""
    try:
        # 初始化配置
        config = ConfigManager('config.yaml')
        
        # 初始化数据库管理器
        db_manager = DatabaseManager(config)
        db_manager.initialize()
        
        # 初始化进度跟踪器
        progress_tracker = ProgressTracker(db_manager)
        
        # 测试创建任务
        print("测试创建任务...")
        task_id = progress_tracker.create_task(
            data_type='basic',
            table_name='stock_basic',
            start_date=None,
            end_date=None,
            total_records=100
        )
        
        print(f"任务创建成功，ID: {task_id}")
        
        # 测试更新进度
        print("测试更新进度...")
        progress_tracker.update_progress(task_id, 50)
        print("进度更新成功")
        
        # 测试完成任务
        print("测试完成任务...")
        progress_tracker.update_task_status(task_id, 'completed')
        print("任务完成成功")
        
        print("进度跟踪器测试成功！")
        
    except Exception as e:
        print(f"进度跟踪器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_progress_tracker()
