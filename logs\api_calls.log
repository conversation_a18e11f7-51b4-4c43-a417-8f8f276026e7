2025-08-03 14:21:13 | 调用API: stock_basic, 参数: {}
2025-08-03 14:21:13 | API调用成功: stock_basic, 耗时: 0.59秒, 返回 5419 条记录
2025-08-03 14:22:33 | 调用API: stock_basic, 参数: {}
2025-08-03 14:22:34 | API调用成功: stock_basic, 耗时: 0.32秒, 返回 5419 条记录
2025-08-03 14:23:28 | 调用API: stock_basic, 参数: {}
2025-08-03 14:23:28 | API调用成功: stock_basic, 耗时: 0.34秒, 返回 5419 条记录
2025-08-03 14:24:57 | 调用API: stock_basic, 参数: {}
2025-08-03 14:24:58 | API调用成功: stock_basic, 耗时: 0.44秒, 返回 5419 条记录
2025-08-03 14:25:47 | 调用API: stock_basic, 参数: {}
2025-08-03 14:25:47 | API调用成功: stock_basic, 耗时: 0.37秒, 返回 5419 条记录
2025-08-03 14:26:31 | 调用API: stock_basic, 参数: {}
2025-08-03 14:26:31 | API调用成功: stock_basic, 耗时: 0.37秒, 返回 5419 条记录
2025-08-03 14:27:09 | 调用API: stock_basic, 参数: {}
2025-08-03 14:27:09 | API调用成功: stock_basic, 耗时: 0.35秒, 返回 5419 条记录
2025-08-03 14:29:14 | 调用API: stock_basic, 参数: {}
2025-08-03 14:29:14 | API调用成功: stock_basic, 耗时: 0.44秒, 返回 5419 条记录
2025-08-03 14:29:43 | 调用API: stock_basic, 参数: {}
2025-08-03 14:29:43 | API调用成功: stock_basic, 耗时: 0.36秒, 返回 5419 条记录
2025-08-03 14:30:43 | 调用API: stock_basic, 参数: {}
2025-08-03 14:30:43 | API调用成功: stock_basic, 耗时: 0.35秒, 返回 5419 条记录
2025-08-03 14:32:25 | 调用API: stock_basic, 参数: {}
2025-08-03 14:32:25 | API调用成功: stock_basic, 耗时: 0.32秒, 返回 5419 条记录
2025-08-03 14:33:01 | 调用API: stock_basic, 参数: {}
2025-08-03 14:33:01 | API调用成功: stock_basic, 耗时: 0.30秒, 返回 5419 条记录
2025-08-03 14:34:15 | 调用API: stock_basic, 参数: {}
2025-08-03 14:34:16 | API调用成功: stock_basic, 耗时: 0.39秒, 返回 5419 条记录
2025-08-03 14:36:48 | 调用API: stock_basic, 参数: {}
2025-08-03 14:36:48 | API调用成功: stock_basic, 耗时: 0.32秒, 返回 5419 条记录
2025-08-03 14:39:00 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20000101', 'end_date': '20001231'}
2025-08-03 14:39:01 | API调用成功: trade_cal, 耗时: 0.15秒, 返回 366 条记录
2025-08-03 14:39:08 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20010101', 'end_date': '20011231'}
2025-08-03 14:39:08 | API调用成功: trade_cal, 耗时: 0.10秒, 返回 365 条记录
2025-08-03 14:39:16 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20020101', 'end_date': '20021231'}
2025-08-03 14:39:16 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 14:39:25 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20030101', 'end_date': '20031231'}
2025-08-03 14:39:25 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 365 条记录
2025-08-03 14:39:32 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20040101', 'end_date': '20041231'}
2025-08-03 14:39:32 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 366 条记录
2025-08-03 14:39:40 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20050101', 'end_date': '20051231'}
2025-08-03 14:39:40 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 14:39:48 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20060101', 'end_date': '20061231'}
2025-08-03 14:39:49 | API调用成功: trade_cal, 耗时: 0.18秒, 返回 365 条记录
2025-08-03 14:39:56 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20070101', 'end_date': '20071231'}
2025-08-03 14:39:57 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 365 条记录
2025-08-03 14:40:04 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20080101', 'end_date': '20081231'}
2025-08-03 14:40:05 | API调用成功: trade_cal, 耗时: 0.16秒, 返回 366 条记录
2025-08-03 14:40:12 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20090101', 'end_date': '20091231'}
2025-08-03 14:40:12 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 14:40:21 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20100101', 'end_date': '20101231'}
2025-08-03 14:40:21 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 14:40:28 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20110101', 'end_date': '20111231'}
2025-08-03 14:40:29 | API调用成功: trade_cal, 耗时: 0.22秒, 返回 365 条记录
2025-08-03 14:40:36 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20120101', 'end_date': '20121231'}
2025-08-03 14:40:36 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 366 条记录
2025-08-03 14:40:44 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20130101', 'end_date': '20131231'}
2025-08-03 14:40:45 | API调用成功: trade_cal, 耗时: 0.10秒, 返回 365 条记录
2025-08-03 14:40:52 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20140101', 'end_date': '20141231'}
2025-08-03 14:40:52 | API调用成功: trade_cal, 耗时: 0.16秒, 返回 365 条记录
2025-08-03 14:41:00 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20150101', 'end_date': '20151231'}
2025-08-03 14:41:00 | API调用成功: trade_cal, 耗时: 0.18秒, 返回 365 条记录
2025-08-03 14:41:08 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20160101', 'end_date': '20161231'}
2025-08-03 14:41:09 | API调用成功: trade_cal, 耗时: 0.14秒, 返回 366 条记录
2025-08-03 14:41:16 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20170101', 'end_date': '20171231'}
2025-08-03 14:41:16 | API调用成功: trade_cal, 耗时: 0.16秒, 返回 365 条记录
2025-08-03 14:41:25 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20180101', 'end_date': '20181231'}
2025-08-03 14:41:25 | API调用成功: trade_cal, 耗时: 0.14秒, 返回 365 条记录
2025-08-03 14:42:42 | 调用API: stock_basic, 参数: {}
2025-08-03 14:42:43 | API调用成功: stock_basic, 耗时: 0.37秒, 返回 5419 条记录
2025-08-03 15:07:41 | 调用API: stock_basic, 参数: {}
2025-08-03 15:07:41 | API调用成功: stock_basic, 耗时: 0.44秒, 返回 5419 条记录
2025-08-03 15:10:12 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20000101', 'end_date': '20001231'}
2025-08-03 15:10:12 | API调用成功: trade_cal, 耗时: 0.18秒, 返回 366 条记录
2025-08-03 15:10:20 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20010101', 'end_date': '20011231'}
2025-08-03 15:10:20 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 15:10:28 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20020101', 'end_date': '20021231'}
2025-08-03 15:10:29 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 365 条记录
2025-08-03 15:10:36 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20030101', 'end_date': '20031231'}
2025-08-03 15:10:37 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 15:10:45 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20040101', 'end_date': '20041231'}
2025-08-03 15:10:45 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 366 条记录
2025-08-03 15:10:54 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20050101', 'end_date': '20051231'}
2025-08-03 15:10:54 | API调用成功: trade_cal, 耗时: 0.15秒, 返回 365 条记录
2025-08-03 15:11:03 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20060101', 'end_date': '20061231'}
2025-08-03 15:11:03 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 365 条记录
2025-08-03 15:11:12 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20070101', 'end_date': '20071231'}
2025-08-03 15:11:12 | API调用成功: trade_cal, 耗时: 0.17秒, 返回 365 条记录
2025-08-03 15:11:21 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20080101', 'end_date': '20081231'}
2025-08-03 15:11:21 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 366 条记录
2025-08-03 15:11:29 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20090101', 'end_date': '20091231'}
2025-08-03 15:11:29 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 365 条记录
2025-08-03 15:11:36 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20100101', 'end_date': '20101231'}
2025-08-03 15:11:37 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 365 条记录
2025-08-03 15:11:46 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20110101', 'end_date': '20111231'}
2025-08-03 15:11:46 | API调用成功: trade_cal, 耗时: 0.14秒, 返回 365 条记录
2025-08-03 15:11:55 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20120101', 'end_date': '20121231'}
2025-08-03 15:11:55 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 366 条记录
2025-08-03 15:12:03 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20130101', 'end_date': '20131231'}
2025-08-03 15:12:03 | API调用成功: trade_cal, 耗时: 0.11秒, 返回 365 条记录
2025-08-03 15:12:11 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20140101', 'end_date': '20141231'}
2025-08-03 15:12:11 | API调用成功: trade_cal, 耗时: 0.14秒, 返回 365 条记录
2025-08-03 15:12:20 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20150101', 'end_date': '20151231'}
2025-08-03 15:12:20 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 15:12:30 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20160101', 'end_date': '20161231'}
2025-08-03 15:12:30 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 366 条记录
2025-08-03 15:12:40 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20170101', 'end_date': '20171231'}
2025-08-03 15:12:40 | API调用成功: trade_cal, 耗时: 0.18秒, 返回 365 条记录
2025-08-03 15:12:49 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20180101', 'end_date': '20181231'}
2025-08-03 15:12:50 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 365 条记录
2025-08-03 15:12:59 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20190101', 'end_date': '20191231'}
2025-08-03 15:12:59 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 15:13:07 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20200101', 'end_date': '20201231'}
2025-08-03 15:13:08 | API调用成功: trade_cal, 耗时: 0.14秒, 返回 366 条记录
2025-08-03 15:13:16 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20210101', 'end_date': '20211231'}
2025-08-03 15:13:16 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 365 条记录
2025-08-03 15:13:24 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20220101', 'end_date': '20221231'}
2025-08-03 15:13:25 | API调用成功: trade_cal, 耗时: 0.18秒, 返回 365 条记录
2025-08-03 15:13:34 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20230101', 'end_date': '20231231'}
2025-08-03 15:13:34 | API调用成功: trade_cal, 耗时: 0.20秒, 返回 365 条记录
2025-08-03 15:13:45 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20240101', 'end_date': '20241231'}
2025-08-03 15:13:45 | API调用成功: trade_cal, 耗时: 0.13秒, 返回 366 条记录
2025-08-03 15:14:01 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20250101', 'end_date': '20251231'}
2025-08-03 15:14:01 | API调用成功: trade_cal, 耗时: 0.12秒, 返回 365 条记录
2025-08-03 15:14:19 | 调用API: trade_cal, 参数: {'exchange': '', 'start_date': '20260101', 'end_date': '20261231'}
2025-08-03 15:14:19 | 调用API: stock_company, 参数: {}
2025-08-03 15:14:21 | API调用成功: stock_company, 耗时: 1.78秒, 返回 6044 条记录
2025-08-03 15:17:36 | 调用API: hs_const, 参数: {'hs_type': 'SH'}
2025-08-03 15:17:36 | API调用成功: hs_const, 耗时: 0.14秒, 返回 581 条记录
2025-08-03 15:17:50 | 调用API: hs_const, 参数: {'hs_type': 'SZ'}
2025-08-03 15:17:50 | API调用成功: hs_const, 耗时: 0.12秒, 返回 242 条记录
2025-08-03 15:17:56 | 调用API: concept, 参数: {'src': 'ts'}
2025-08-03 15:17:56 | API调用成功: concept, 耗时: 0.11秒, 返回 879 条记录
2025-08-03 15:18:17 | 调用API: concept_detail, 参数: {'id': 'TS0'}
2025-08-03 15:18:17 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:18:18 | 调用API: concept_detail, 参数: {'id': 'TS1'}
2025-08-03 15:18:18 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 39 条记录
2025-08-03 15:18:19 | 调用API: concept_detail, 参数: {'id': 'TS2'}
2025-08-03 15:18:20 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 10 条记录
2025-08-03 15:18:20 | 调用API: concept_detail, 参数: {'id': 'TS3'}
2025-08-03 15:18:20 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 8 条记录
2025-08-03 15:18:20 | 调用API: concept_detail, 参数: {'id': 'TS4'}
2025-08-03 15:18:21 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 54 条记录
2025-08-03 15:18:22 | 调用API: concept_detail, 参数: {'id': 'TS5'}
2025-08-03 15:18:22 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:18:22 | 调用API: concept_detail, 参数: {'id': 'TS6'}
2025-08-03 15:18:22 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 3 条记录
2025-08-03 15:18:23 | 调用API: concept_detail, 参数: {'id': 'TS7'}
2025-08-03 15:18:23 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 4 条记录
2025-08-03 15:18:23 | 调用API: concept_detail, 参数: {'id': 'TS8'}
2025-08-03 15:18:23 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 68 条记录
2025-08-03 15:18:25 | 调用API: concept_detail, 参数: {'id': 'TS9'}
2025-08-03 15:18:25 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 116 条记录
2025-08-03 15:18:27 | 调用API: concept_detail, 参数: {'id': 'TS10'}
2025-08-03 15:18:27 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 1 条记录
2025-08-03 15:18:27 | 调用API: concept_detail, 参数: {'id': 'TS11'}
2025-08-03 15:18:28 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:18:28 | 调用API: concept_detail, 参数: {'id': 'TS12'}
2025-08-03 15:18:28 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 126 条记录
2025-08-03 15:18:31 | 调用API: concept_detail, 参数: {'id': 'TS13'}
2025-08-03 15:18:31 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 28 条记录
2025-08-03 15:18:32 | 调用API: concept_detail, 参数: {'id': 'TS14'}
2025-08-03 15:18:32 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 15 条记录
2025-08-03 15:18:32 | 调用API: concept_detail, 参数: {'id': 'TS15'}
2025-08-03 15:18:32 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:18:32 | 调用API: concept_detail, 参数: {'id': 'TS16'}
2025-08-03 15:18:33 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 5 条记录
2025-08-03 15:18:33 | 调用API: concept_detail, 参数: {'id': 'TS17'}
2025-08-03 15:18:33 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:18:33 | 调用API: concept_detail, 参数: {'id': 'TS18'}
2025-08-03 15:18:33 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:18:33 | 调用API: concept_detail, 参数: {'id': 'TS19'}
2025-08-03 15:18:34 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 24 条记录
2025-08-03 15:18:34 | 调用API: concept_detail, 参数: {'id': 'TS20'}
2025-08-03 15:18:34 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 36 条记录
2025-08-03 15:18:35 | 调用API: concept_detail, 参数: {'id': 'TS21'}
2025-08-03 15:18:35 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:18:36 | 调用API: concept_detail, 参数: {'id': 'TS22'}
2025-08-03 15:18:36 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 7 条记录
2025-08-03 15:18:36 | 调用API: concept_detail, 参数: {'id': 'TS23'}
2025-08-03 15:18:36 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 32 条记录
2025-08-03 15:18:37 | 调用API: concept_detail, 参数: {'id': 'TS24'}
2025-08-03 15:18:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 2 条记录
2025-08-03 15:18:37 | 调用API: concept_detail, 参数: {'id': 'TS25'}
2025-08-03 15:18:38 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 111 条记录
2025-08-03 15:18:40 | 调用API: concept_detail, 参数: {'id': 'TS26'}
2025-08-03 15:18:40 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 7 条记录
2025-08-03 15:18:40 | 调用API: concept_detail, 参数: {'id': 'TS27'}
2025-08-03 15:18:40 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 10 条记录
2025-08-03 15:18:41 | 调用API: concept_detail, 参数: {'id': 'TS28'}
2025-08-03 15:18:41 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 38 条记录
2025-08-03 15:18:42 | 调用API: concept_detail, 参数: {'id': 'TS29'}
2025-08-03 15:18:42 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 10 条记录
2025-08-03 15:18:42 | 调用API: concept_detail, 参数: {'id': 'TS30'}
2025-08-03 15:18:43 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 5 条记录
2025-08-03 15:18:43 | 调用API: concept_detail, 参数: {'id': 'TS31'}
2025-08-03 15:18:43 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 2 条记录
2025-08-03 15:18:43 | 调用API: concept_detail, 参数: {'id': 'TS32'}
2025-08-03 15:18:43 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 11 条记录
2025-08-03 15:18:44 | 调用API: concept_detail, 参数: {'id': 'TS33'}
2025-08-03 15:18:44 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 12 条记录
2025-08-03 15:18:44 | 调用API: concept_detail, 参数: {'id': 'TS34'}
2025-08-03 15:18:44 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 22 条记录
2025-08-03 15:18:45 | 调用API: concept_detail, 参数: {'id': 'TS35'}
2025-08-03 15:18:45 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 132 条记录
2025-08-03 15:18:48 | 调用API: concept_detail, 参数: {'id': 'TS36'}
2025-08-03 15:18:48 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 13 条记录
2025-08-03 15:18:48 | 调用API: concept_detail, 参数: {'id': 'TS37'}
2025-08-03 15:18:49 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 4 条记录
2025-08-03 15:18:49 | 调用API: concept_detail, 参数: {'id': 'TS38'}
2025-08-03 15:18:49 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 20 条记录
2025-08-03 15:18:50 | 调用API: concept_detail, 参数: {'id': 'TS39'}
2025-08-03 15:18:50 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 27 条记录
2025-08-03 15:18:50 | 调用API: concept_detail, 参数: {'id': 'TS40'}
2025-08-03 15:18:51 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:18:51 | 调用API: concept_detail, 参数: {'id': 'TS41'}
2025-08-03 15:18:51 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:18:51 | 调用API: concept_detail, 参数: {'id': 'TS42'}
2025-08-03 15:18:51 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 9 条记录
2025-08-03 15:18:52 | 调用API: concept_detail, 参数: {'id': 'TS43'}
2025-08-03 15:18:52 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 6 条记录
2025-08-03 15:18:52 | 调用API: concept_detail, 参数: {'id': 'TS44'}
2025-08-03 15:18:53 | API调用成功: concept_detail, 耗时: 0.51秒, 返回 5 条记录
2025-08-03 15:18:53 | 调用API: concept_detail, 参数: {'id': 'TS45'}
2025-08-03 15:18:53 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 3 条记录
2025-08-03 15:18:53 | 调用API: concept_detail, 参数: {'id': 'TS46'}
2025-08-03 15:18:53 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 9 条记录
2025-08-03 15:18:53 | 调用API: concept_detail, 参数: {'id': 'TS47'}
2025-08-03 15:18:54 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 2 条记录
2025-08-03 15:18:54 | 调用API: concept_detail, 参数: {'id': 'TS48'}
2025-08-03 15:18:54 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 94 条记录
2025-08-03 15:18:56 | 调用API: concept_detail, 参数: {'id': 'TS49'}
2025-08-03 15:18:56 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 4 条记录
2025-08-03 15:18:56 | 调用API: concept_detail, 参数: {'id': 'TS50'}
2025-08-03 15:18:56 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 9 条记录
2025-08-03 15:18:57 | 调用API: concept_detail, 参数: {'id': 'TS51'}
2025-08-03 15:18:57 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 34 条记录
2025-08-03 15:18:57 | 调用API: concept_detail, 参数: {'id': 'TS52'}
2025-08-03 15:18:58 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 21 条记录
2025-08-03 15:18:58 | 调用API: concept_detail, 参数: {'id': 'TS53'}
2025-08-03 15:18:58 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 82 条记录
2025-08-03 15:19:00 | 调用API: concept_detail, 参数: {'id': 'TS54'}
2025-08-03 15:19:00 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 6 条记录
2025-08-03 15:19:01 | 调用API: concept_detail, 参数: {'id': 'TS55'}
2025-08-03 15:19:01 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:19:01 | 调用API: concept_detail, 参数: {'id': 'TS56'}
2025-08-03 15:19:01 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 9 条记录
2025-08-03 15:19:02 | 调用API: concept_detail, 参数: {'id': 'TS57'}
2025-08-03 15:19:02 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:19:03 | 调用API: concept_detail, 参数: {'id': 'TS58'}
2025-08-03 15:19:03 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 15 条记录
2025-08-03 15:19:03 | 调用API: concept_detail, 参数: {'id': 'TS59'}
2025-08-03 15:19:03 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 20 条记录
2025-08-03 15:19:04 | 调用API: concept_detail, 参数: {'id': 'TS60'}
2025-08-03 15:19:04 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 26 条记录
2025-08-03 15:19:05 | 调用API: concept_detail, 参数: {'id': 'TS61'}
2025-08-03 15:19:05 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 33 条记录
2025-08-03 15:19:06 | 调用API: concept_detail, 参数: {'id': 'TS62'}
2025-08-03 15:19:06 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 7 条记录
2025-08-03 15:19:07 | 调用API: concept_detail, 参数: {'id': 'TS63'}
2025-08-03 15:19:07 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 2 条记录
2025-08-03 15:19:07 | 调用API: concept_detail, 参数: {'id': 'TS64'}
2025-08-03 15:19:07 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 8 条记录
2025-08-03 15:19:07 | 调用API: concept_detail, 参数: {'id': 'TS65'}
2025-08-03 15:19:08 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:19:08 | 调用API: concept_detail, 参数: {'id': 'TS66'}
2025-08-03 15:19:08 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 10 条记录
2025-08-03 15:19:08 | 调用API: concept_detail, 参数: {'id': 'TS67'}
2025-08-03 15:19:08 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 24 条记录
2025-08-03 15:19:09 | 调用API: concept_detail, 参数: {'id': 'TS68'}
2025-08-03 15:19:09 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 6 条记录
2025-08-03 15:19:10 | 调用API: concept_detail, 参数: {'id': 'TS69'}
2025-08-03 15:19:10 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 6 条记录
2025-08-03 15:19:10 | 调用API: concept_detail, 参数: {'id': 'TS70'}
2025-08-03 15:19:10 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 36 条记录
2025-08-03 15:19:11 | 调用API: concept_detail, 参数: {'id': 'TS71'}
2025-08-03 15:19:11 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 26 条记录
2025-08-03 15:19:12 | 调用API: concept_detail, 参数: {'id': 'TS72'}
2025-08-03 15:19:12 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:19:13 | 调用API: concept_detail, 参数: {'id': 'TS73'}
2025-08-03 15:19:13 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 29 条记录
2025-08-03 15:19:13 | 调用API: concept_detail, 参数: {'id': 'TS74'}
2025-08-03 15:19:14 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 11 条记录
2025-08-03 15:19:14 | 调用API: concept_detail, 参数: {'id': 'TS75'}
2025-08-03 15:19:14 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 16 条记录
2025-08-03 15:19:14 | 调用API: concept_detail, 参数: {'id': 'TS76'}
2025-08-03 15:19:15 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 4 条记录
2025-08-03 15:19:15 | 调用API: concept_detail, 参数: {'id': 'TS77'}
2025-08-03 15:19:15 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 13 条记录
2025-08-03 15:19:15 | 调用API: concept_detail, 参数: {'id': 'TS78'}
2025-08-03 15:19:15 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:19:16 | 调用API: concept_detail, 参数: {'id': 'TS79'}
2025-08-03 15:19:16 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:19:16 | 调用API: concept_detail, 参数: {'id': 'TS80'}
2025-08-03 15:19:16 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 3 条记录
2025-08-03 15:19:16 | 调用API: concept_detail, 参数: {'id': 'TS81'}
2025-08-03 15:19:16 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 3 条记录
2025-08-03 15:19:16 | 调用API: concept_detail, 参数: {'id': 'TS82'}
2025-08-03 15:19:17 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 26 条记录
2025-08-03 15:19:17 | 调用API: concept_detail, 参数: {'id': 'TS83'}
2025-08-03 15:19:17 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 57 条记录
2025-08-03 15:19:19 | 调用API: concept_detail, 参数: {'id': 'TS84'}
2025-08-03 15:19:19 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 10 条记录
2025-08-03 15:19:19 | 调用API: concept_detail, 参数: {'id': 'TS85'}
2025-08-03 15:19:19 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 15 条记录
2025-08-03 15:19:20 | 调用API: concept_detail, 参数: {'id': 'TS86'}
2025-08-03 15:19:20 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 4 条记录
2025-08-03 15:19:20 | 调用API: concept_detail, 参数: {'id': 'TS87'}
2025-08-03 15:19:20 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 3 条记录
2025-08-03 15:19:21 | 调用API: concept_detail, 参数: {'id': 'TS88'}
2025-08-03 15:19:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 5 条记录
2025-08-03 15:19:21 | 调用API: concept_detail, 参数: {'id': 'TS89'}
2025-08-03 15:19:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 13 条记录
2025-08-03 15:19:21 | 调用API: concept_detail, 参数: {'id': 'TS90'}
2025-08-03 15:19:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1 条记录
2025-08-03 15:19:22 | 调用API: concept_detail, 参数: {'id': 'TS91'}
2025-08-03 15:19:22 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 39 条记录
2025-08-03 15:19:23 | 调用API: concept_detail, 参数: {'id': 'TS92'}
2025-08-03 15:19:23 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:19:23 | 调用API: concept_detail, 参数: {'id': 'TS93'}
2025-08-03 15:19:23 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 14 条记录
2025-08-03 15:19:24 | 调用API: concept_detail, 参数: {'id': 'TS94'}
2025-08-03 15:19:24 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 13 条记录
2025-08-03 15:19:24 | 调用API: concept_detail, 参数: {'id': 'TS95'}
2025-08-03 15:19:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 5 条记录
2025-08-03 15:19:24 | 调用API: concept_detail, 参数: {'id': 'TS96'}
2025-08-03 15:19:25 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:19:25 | 调用API: concept_detail, 参数: {'id': 'TS97'}
2025-08-03 15:19:25 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1 条记录
2025-08-03 15:19:25 | 调用API: concept_detail, 参数: {'id': 'TS98'}
2025-08-03 15:19:26 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 11 条记录
2025-08-03 15:19:26 | 调用API: concept_detail, 参数: {'id': 'TS99'}
2025-08-03 15:19:26 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 31 条记录
2025-08-03 15:19:27 | 调用API: concept_detail, 参数: {'id': 'TS100'}
2025-08-03 15:19:27 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:19:28 | 调用API: concept_detail, 参数: {'id': 'TS101'}
2025-08-03 15:19:28 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 27 条记录
2025-08-03 15:19:28 | 调用API: concept_detail, 参数: {'id': 'TS102'}
2025-08-03 15:19:28 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 6 条记录
2025-08-03 15:19:29 | 调用API: concept_detail, 参数: {'id': 'TS103'}
2025-08-03 15:19:29 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 50 条记录
2025-08-03 15:19:30 | 调用API: concept_detail, 参数: {'id': 'TS104'}
2025-08-03 15:19:30 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 20 条记录
2025-08-03 15:19:31 | 调用API: concept_detail, 参数: {'id': 'TS105'}
2025-08-03 15:19:31 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 10 条记录
2025-08-03 15:19:32 | 调用API: concept_detail, 参数: {'id': 'TS106'}
2025-08-03 15:19:32 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 86 条记录
2025-08-03 15:19:34 | 调用API: concept_detail, 参数: {'id': 'TS107'}
2025-08-03 15:19:34 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 15 条记录
2025-08-03 15:19:34 | 调用API: concept_detail, 参数: {'id': 'TS108'}
2025-08-03 15:19:35 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 1000 条记录
2025-08-03 15:19:55 | 调用API: concept_detail, 参数: {'id': 'TS109'}
2025-08-03 15:19:55 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 11 条记录
2025-08-03 15:19:56 | 调用API: concept_detail, 参数: {'id': 'TS110'}
2025-08-03 15:19:56 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 123 条记录
2025-08-03 15:19:58 | 调用API: concept_detail, 参数: {'id': 'TS111'}
2025-08-03 15:19:59 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 77 条记录
2025-08-03 15:20:00 | 调用API: concept_detail, 参数: {'id': 'TS112'}
2025-08-03 15:20:00 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 79 条记录
2025-08-03 15:20:02 | 调用API: concept_detail, 参数: {'id': 'TS113'}
2025-08-03 15:20:02 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 23 条记录
2025-08-03 15:20:02 | 调用API: concept_detail, 参数: {'id': 'TS114'}
2025-08-03 15:20:03 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 7 条记录
2025-08-03 15:20:03 | 调用API: concept_detail, 参数: {'id': 'TS115'}
2025-08-03 15:20:03 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 10 条记录
2025-08-03 15:20:03 | 调用API: concept_detail, 参数: {'id': 'TS116'}
2025-08-03 15:20:04 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 21 条记录
2025-08-03 15:20:04 | 调用API: concept_detail, 参数: {'id': 'TS117'}
2025-08-03 15:20:04 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 26 条记录
2025-08-03 15:20:05 | 调用API: concept_detail, 参数: {'id': 'TS118'}
2025-08-03 15:20:05 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 27 条记录
2025-08-03 15:20:06 | 调用API: concept_detail, 参数: {'id': 'TS119'}
2025-08-03 15:20:06 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 9 条记录
2025-08-03 15:20:07 | 调用API: concept_detail, 参数: {'id': 'TS120'}
2025-08-03 15:20:07 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 88 条记录
2025-08-03 15:20:10 | 调用API: concept_detail, 参数: {'id': 'TS121'}
2025-08-03 15:20:10 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:20:10 | 调用API: concept_detail, 参数: {'id': 'TS122'}
2025-08-03 15:20:10 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 11 条记录
2025-08-03 15:20:11 | 调用API: concept_detail, 参数: {'id': 'TS123'}
2025-08-03 15:20:11 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 12 条记录
2025-08-03 15:20:11 | 调用API: concept_detail, 参数: {'id': 'TS124'}
2025-08-03 15:20:11 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 32 条记录
2025-08-03 15:20:12 | 调用API: concept_detail, 参数: {'id': 'TS125'}
2025-08-03 15:20:12 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 5 条记录
2025-08-03 15:20:13 | 调用API: concept_detail, 参数: {'id': 'TS126'}
2025-08-03 15:20:13 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 12 条记录
2025-08-03 15:20:13 | 调用API: concept_detail, 参数: {'id': 'TS127'}
2025-08-03 15:20:13 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 4 条记录
2025-08-03 15:20:14 | 调用API: concept_detail, 参数: {'id': 'TS128'}
2025-08-03 15:20:14 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 14 条记录
2025-08-03 15:20:14 | 调用API: concept_detail, 参数: {'id': 'TS129'}
2025-08-03 15:20:14 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 32 条记录
2025-08-03 15:20:15 | 调用API: concept_detail, 参数: {'id': 'TS130'}
2025-08-03 15:20:16 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 4 条记录
2025-08-03 15:20:16 | 调用API: concept_detail, 参数: {'id': 'TS131'}
2025-08-03 15:20:16 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:20:16 | 调用API: concept_detail, 参数: {'id': 'TS132'}
2025-08-03 15:20:16 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 4 条记录
2025-08-03 15:20:16 | 调用API: concept_detail, 参数: {'id': 'TS133'}
2025-08-03 15:20:17 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 120 条记录
2025-08-03 15:20:19 | 调用API: concept_detail, 参数: {'id': 'TS134'}
2025-08-03 15:20:19 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 27 条记录
2025-08-03 15:20:20 | 调用API: concept_detail, 参数: {'id': 'TS135'}
2025-08-03 15:20:20 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 10 条记录
2025-08-03 15:20:20 | 调用API: concept_detail, 参数: {'id': 'TS136'}
2025-08-03 15:20:20 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 42 条记录
2025-08-03 15:20:21 | 调用API: concept_detail, 参数: {'id': 'TS137'}
2025-08-03 15:20:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 2 条记录
2025-08-03 15:20:21 | 调用API: concept_detail, 参数: {'id': 'TS138'}
2025-08-03 15:20:21 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 19 条记录
2025-08-03 15:20:22 | 调用API: concept_detail, 参数: {'id': 'TS139'}
2025-08-03 15:20:22 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 7 条记录
2025-08-03 15:20:22 | 调用API: concept_detail, 参数: {'id': 'TS140'}
2025-08-03 15:20:22 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 11 条记录
2025-08-03 15:20:23 | 调用API: concept_detail, 参数: {'id': 'TS141'}
2025-08-03 15:20:23 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 50 条记录
2025-08-03 15:20:24 | 调用API: concept_detail, 参数: {'id': 'TS142'}
2025-08-03 15:20:24 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:20:24 | 调用API: concept_detail, 参数: {'id': 'TS143'}
2025-08-03 15:20:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 1000 条记录
2025-08-03 15:20:47 | 调用API: concept_detail, 参数: {'id': 'TS144'}
2025-08-03 15:20:47 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 82 条记录
2025-08-03 15:20:49 | 调用API: concept_detail, 参数: {'id': 'TS145'}
2025-08-03 15:20:49 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 1000 条记录
2025-08-03 15:21:11 | 调用API: concept_detail, 参数: {'id': 'TS146'}
2025-08-03 15:21:11 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 5 条记录
2025-08-03 15:21:11 | 调用API: concept_detail, 参数: {'id': 'TS147'}
2025-08-03 15:21:11 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1000 条记录
2025-08-03 15:21:33 | 调用API: concept_detail, 参数: {'id': 'TS148'}
2025-08-03 15:21:33 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 53 条记录
2025-08-03 15:21:35 | 调用API: concept_detail, 参数: {'id': 'TS149'}
2025-08-03 15:21:35 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 9 条记录
2025-08-03 15:21:36 | 调用API: concept_detail, 参数: {'id': 'TS150'}
2025-08-03 15:21:36 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 10 条记录
2025-08-03 15:21:36 | 调用API: concept_detail, 参数: {'id': 'TS151'}
2025-08-03 15:21:36 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 4 条记录
2025-08-03 15:21:36 | 调用API: concept_detail, 参数: {'id': 'TS152'}
2025-08-03 15:21:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:21:37 | 调用API: concept_detail, 参数: {'id': 'TS153'}
2025-08-03 15:21:37 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 53 条记录
2025-08-03 15:21:38 | 调用API: concept_detail, 参数: {'id': 'TS154'}
2025-08-03 15:21:38 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 30 条记录
2025-08-03 15:21:39 | 调用API: concept_detail, 参数: {'id': 'TS155'}
2025-08-03 15:21:39 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:21:39 | 调用API: concept_detail, 参数: {'id': 'TS156'}
2025-08-03 15:21:40 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 12 条记录
2025-08-03 15:21:40 | 调用API: concept_detail, 参数: {'id': 'TS157'}
2025-08-03 15:21:40 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:21:40 | 调用API: concept_detail, 参数: {'id': 'TS158'}
2025-08-03 15:21:41 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 18 条记录
2025-08-03 15:21:41 | 调用API: concept_detail, 参数: {'id': 'TS159'}
2025-08-03 15:21:41 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 98 条记录
2025-08-03 15:21:43 | 调用API: concept_detail, 参数: {'id': 'TS160'}
2025-08-03 15:21:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:21:44 | 调用API: concept_detail, 参数: {'id': 'TS161'}
2025-08-03 15:21:44 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 15 条记录
2025-08-03 15:21:44 | 调用API: concept_detail, 参数: {'id': 'TS162'}
2025-08-03 15:21:44 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 189 条记录
2025-08-03 15:21:48 | 调用API: concept_detail, 参数: {'id': 'TS163'}
2025-08-03 15:21:48 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 74 条记录
2025-08-03 15:21:50 | 调用API: concept_detail, 参数: {'id': 'TS164'}
2025-08-03 15:21:50 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 11 条记录
2025-08-03 15:21:50 | 调用API: concept_detail, 参数: {'id': 'TS165'}
2025-08-03 15:21:51 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 11 条记录
2025-08-03 15:21:51 | 调用API: concept_detail, 参数: {'id': 'TS166'}
2025-08-03 15:21:51 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 15 条记录
2025-08-03 15:21:52 | 调用API: concept_detail, 参数: {'id': 'TS167'}
2025-08-03 15:21:52 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 20 条记录
2025-08-03 15:21:52 | 调用API: concept_detail, 参数: {'id': 'TS168'}
2025-08-03 15:21:52 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 63 条记录
2025-08-03 15:21:54 | 调用API: concept_detail, 参数: {'id': 'TS169'}
2025-08-03 15:21:54 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 8 条记录
2025-08-03 15:21:54 | 调用API: concept_detail, 参数: {'id': 'TS170'}
2025-08-03 15:21:55 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 34 条记录
2025-08-03 15:21:56 | 调用API: concept_detail, 参数: {'id': 'TS171'}
2025-08-03 15:21:56 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:21:56 | 调用API: concept_detail, 参数: {'id': 'TS172'}
2025-08-03 15:21:56 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 72 条记录
2025-08-03 15:21:58 | 调用API: concept_detail, 参数: {'id': 'TS173'}
2025-08-03 15:21:58 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 10 条记录
2025-08-03 15:21:59 | 调用API: concept_detail, 参数: {'id': 'TS174'}
2025-08-03 15:21:59 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 14 条记录
2025-08-03 15:21:59 | 调用API: concept_detail, 参数: {'id': 'TS175'}
2025-08-03 15:22:00 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 11 条记录
2025-08-03 15:22:00 | 调用API: concept_detail, 参数: {'id': 'TS176'}
2025-08-03 15:22:00 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 23 条记录
2025-08-03 15:22:01 | 调用API: concept_detail, 参数: {'id': 'TS177'}
2025-08-03 15:22:01 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 17 条记录
2025-08-03 15:22:02 | 调用API: concept_detail, 参数: {'id': 'TS178'}
2025-08-03 15:22:02 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 3 条记录
2025-08-03 15:22:02 | 调用API: concept_detail, 参数: {'id': 'TS179'}
2025-08-03 15:22:02 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 99 条记录
2025-08-03 15:22:05 | 调用API: concept_detail, 参数: {'id': 'TS180'}
2025-08-03 15:22:06 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 28 条记录
2025-08-03 15:22:07 | 调用API: concept_detail, 参数: {'id': 'TS181'}
2025-08-03 15:22:07 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:22:07 | 调用API: concept_detail, 参数: {'id': 'TS182'}
2025-08-03 15:22:07 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 38 条记录
2025-08-03 15:22:08 | 调用API: concept_detail, 参数: {'id': 'TS183'}
2025-08-03 15:22:09 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:22:09 | 调用API: concept_detail, 参数: {'id': 'TS184'}
2025-08-03 15:22:09 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 10 条记录
2025-08-03 15:22:09 | 调用API: concept_detail, 参数: {'id': 'TS185'}
2025-08-03 15:22:09 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 25 条记录
2025-08-03 15:22:10 | 调用API: concept_detail, 参数: {'id': 'TS186'}
2025-08-03 15:22:10 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 364 条记录
2025-08-03 15:22:21 | 调用API: concept_detail, 参数: {'id': 'TS187'}
2025-08-03 15:22:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 14 条记录
2025-08-03 15:22:21 | 调用API: concept_detail, 参数: {'id': 'TS188'}
2025-08-03 15:22:21 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 6 条记录
2025-08-03 15:22:22 | 调用API: concept_detail, 参数: {'id': 'TS189'}
2025-08-03 15:22:22 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 11 条记录
2025-08-03 15:22:22 | 调用API: concept_detail, 参数: {'id': 'TS190'}
2025-08-03 15:22:22 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 9 条记录
2025-08-03 15:22:23 | 调用API: concept_detail, 参数: {'id': 'TS191'}
2025-08-03 15:22:23 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 24 条记录
2025-08-03 15:22:24 | 调用API: concept_detail, 参数: {'id': 'TS192'}
2025-08-03 15:22:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:22:24 | 调用API: concept_detail, 参数: {'id': 'TS193'}
2025-08-03 15:22:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:22:25 | 调用API: concept_detail, 参数: {'id': 'TS194'}
2025-08-03 15:22:25 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 52 条记录
2025-08-03 15:22:26 | 调用API: concept_detail, 参数: {'id': 'TS195'}
2025-08-03 15:22:26 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 18 条记录
2025-08-03 15:22:26 | 调用API: concept_detail, 参数: {'id': 'TS196'}
2025-08-03 15:22:27 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 58 条记录
2025-08-03 15:22:28 | 调用API: concept_detail, 参数: {'id': 'TS197'}
2025-08-03 15:22:28 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 10 条记录
2025-08-03 15:22:28 | 调用API: concept_detail, 参数: {'id': 'TS198'}
2025-08-03 15:22:29 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 60 条记录
2025-08-03 15:22:30 | 调用API: concept_detail, 参数: {'id': 'TS199'}
2025-08-03 15:22:30 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 15 条记录
2025-08-03 15:22:31 | 调用API: concept_detail, 参数: {'id': 'TS200'}
2025-08-03 15:22:31 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 63 条记录
2025-08-03 15:22:32 | 调用API: concept_detail, 参数: {'id': 'TS201'}
2025-08-03 15:22:32 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 68 条记录
2025-08-03 15:22:34 | 调用API: concept_detail, 参数: {'id': 'TS202'}
2025-08-03 15:22:34 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 81 条记录
2025-08-03 15:22:36 | 调用API: concept_detail, 参数: {'id': 'TS203'}
2025-08-03 15:22:36 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 3 条记录
2025-08-03 15:22:36 | 调用API: concept_detail, 参数: {'id': 'TS204'}
2025-08-03 15:22:36 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 13 条记录
2025-08-03 15:22:36 | 调用API: concept_detail, 参数: {'id': 'TS205'}
2025-08-03 15:22:37 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 14 条记录
2025-08-03 15:22:37 | 调用API: concept_detail, 参数: {'id': 'TS206'}
2025-08-03 15:22:37 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 73 条记录
2025-08-03 15:22:39 | 调用API: concept_detail, 参数: {'id': 'TS207'}
2025-08-03 15:22:39 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:22:39 | 调用API: concept_detail, 参数: {'id': 'TS208'}
2025-08-03 15:22:39 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 5 条记录
2025-08-03 15:22:39 | 调用API: concept_detail, 参数: {'id': 'TS209'}
2025-08-03 15:22:40 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 35 条记录
2025-08-03 15:22:40 | 调用API: concept_detail, 参数: {'id': 'TS210'}
2025-08-03 15:22:41 | 调用API: concept_detail, 参数: {'id': 'TS211'}
2025-08-03 15:22:41 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 26 条记录
2025-08-03 15:22:41 | 调用API: concept_detail, 参数: {'id': 'TS212'}
2025-08-03 15:22:41 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 2 条记录
2025-08-03 15:22:42 | 调用API: concept_detail, 参数: {'id': 'TS213'}
2025-08-03 15:22:42 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 26 条记录
2025-08-03 15:22:42 | 调用API: concept_detail, 参数: {'id': 'TS214'}
2025-08-03 15:22:43 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 3 条记录
2025-08-03 15:22:43 | 调用API: concept_detail, 参数: {'id': 'TS215'}
2025-08-03 15:22:43 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 18 条记录
2025-08-03 15:22:44 | 调用API: concept_detail, 参数: {'id': 'TS216'}
2025-08-03 15:22:44 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 10 条记录
2025-08-03 15:22:45 | 调用API: concept_detail, 参数: {'id': 'TS217'}
2025-08-03 15:22:45 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 3 条记录
2025-08-03 15:22:45 | 调用API: concept_detail, 参数: {'id': 'TS218'}
2025-08-03 15:22:45 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 5 条记录
2025-08-03 15:22:46 | 调用API: concept_detail, 参数: {'id': 'TS219'}
2025-08-03 15:22:46 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 31 条记录
2025-08-03 15:22:47 | 调用API: concept_detail, 参数: {'id': 'TS220'}
2025-08-03 15:22:47 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 89 条记录
2025-08-03 15:22:49 | 调用API: concept_detail, 参数: {'id': 'TS221'}
2025-08-03 15:22:49 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 9 条记录
2025-08-03 15:22:49 | 调用API: concept_detail, 参数: {'id': 'TS222'}
2025-08-03 15:22:50 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 6 条记录
2025-08-03 15:22:50 | 调用API: concept_detail, 参数: {'id': 'TS223'}
2025-08-03 15:22:50 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 35 条记录
2025-08-03 15:22:51 | 调用API: concept_detail, 参数: {'id': 'TS224'}
2025-08-03 15:22:51 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:22:51 | 调用API: concept_detail, 参数: {'id': 'TS225'}
2025-08-03 15:22:51 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 16 条记录
2025-08-03 15:22:52 | 调用API: concept_detail, 参数: {'id': 'TS226'}
2025-08-03 15:22:52 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 38 条记录
2025-08-03 15:22:53 | 调用API: concept_detail, 参数: {'id': 'TS227'}
2025-08-03 15:22:53 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 48 条记录
2025-08-03 15:22:55 | 调用API: concept_detail, 参数: {'id': 'TS228'}
2025-08-03 15:22:55 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 59 条记录
2025-08-03 15:22:56 | 调用API: concept_detail, 参数: {'id': 'TS229'}
2025-08-03 15:22:56 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 7 条记录
2025-08-03 15:22:57 | 调用API: concept_detail, 参数: {'id': 'TS230'}
2025-08-03 15:22:57 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 9 条记录
2025-08-03 15:22:57 | 调用API: concept_detail, 参数: {'id': 'TS231'}
2025-08-03 15:22:57 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 65 条记录
2025-08-03 15:22:59 | 调用API: concept_detail, 参数: {'id': 'TS232'}
2025-08-03 15:22:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:22:59 | 调用API: concept_detail, 参数: {'id': 'TS233'}
2025-08-03 15:22:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:22:59 | 调用API: concept_detail, 参数: {'id': 'TS234'}
2025-08-03 15:22:59 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 27 条记录
2025-08-03 15:23:00 | 调用API: concept_detail, 参数: {'id': 'TS235'}
2025-08-03 15:23:00 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 2 条记录
2025-08-03 15:23:00 | 调用API: concept_detail, 参数: {'id': 'TS236'}
2025-08-03 15:23:00 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 145 条记录
2025-08-03 15:23:03 | 调用API: concept_detail, 参数: {'id': 'TS237'}
2025-08-03 15:23:04 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:23:04 | 调用API: concept_detail, 参数: {'id': 'TS238'}
2025-08-03 15:23:04 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 30 条记录
2025-08-03 15:23:05 | 调用API: concept_detail, 参数: {'id': 'TS239'}
2025-08-03 15:23:05 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 13 条记录
2025-08-03 15:23:05 | 调用API: concept_detail, 参数: {'id': 'TS240'}
2025-08-03 15:23:05 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:23:06 | 调用API: concept_detail, 参数: {'id': 'TS241'}
2025-08-03 15:23:06 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 12 条记录
2025-08-03 15:23:06 | 调用API: concept_detail, 参数: {'id': 'TS242'}
2025-08-03 15:23:06 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 19 条记录
2025-08-03 15:23:07 | 调用API: concept_detail, 参数: {'id': 'TS243'}
2025-08-03 15:23:07 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 7 条记录
2025-08-03 15:23:07 | 调用API: concept_detail, 参数: {'id': 'TS244'}
2025-08-03 15:23:07 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 27 条记录
2025-08-03 15:23:08 | 调用API: concept_detail, 参数: {'id': 'TS245'}
2025-08-03 15:23:08 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 16 条记录
2025-08-03 15:23:09 | 调用API: concept_detail, 参数: {'id': 'TS246'}
2025-08-03 15:23:09 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 13 条记录
2025-08-03 15:23:09 | 调用API: concept_detail, 参数: {'id': 'TS247'}
2025-08-03 15:23:09 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 20 条记录
2025-08-03 15:23:10 | 调用API: concept_detail, 参数: {'id': 'TS248'}
2025-08-03 15:23:10 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 15 条记录
2025-08-03 15:23:10 | 调用API: concept_detail, 参数: {'id': 'TS249'}
2025-08-03 15:23:11 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 27 条记录
2025-08-03 15:23:11 | 调用API: concept_detail, 参数: {'id': 'TS250'}
2025-08-03 15:23:11 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 2 条记录
2025-08-03 15:23:12 | 调用API: concept_detail, 参数: {'id': 'TS251'}
2025-08-03 15:23:12 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 2 条记录
2025-08-03 15:23:12 | 调用API: concept_detail, 参数: {'id': 'TS252'}
2025-08-03 15:23:12 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 2 条记录
2025-08-03 15:23:12 | 调用API: concept_detail, 参数: {'id': 'TS253'}
2025-08-03 15:23:12 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:23:12 | 调用API: concept_detail, 参数: {'id': 'TS254'}
2025-08-03 15:23:13 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 5 条记录
2025-08-03 15:23:13 | 调用API: concept_detail, 参数: {'id': 'TS255'}
2025-08-03 15:23:13 | API调用成功: concept_detail, 耗时: 0.25秒, 返回 7 条记录
2025-08-03 15:23:13 | 调用API: concept_detail, 参数: {'id': 'TS256'}
2025-08-03 15:23:14 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 10 条记录
2025-08-03 15:23:14 | 调用API: concept_detail, 参数: {'id': 'TS257'}
2025-08-03 15:23:14 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:23:14 | 调用API: concept_detail, 参数: {'id': 'TS258'}
2025-08-03 15:23:14 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 62 条记录
2025-08-03 15:23:15 | 调用API: concept_detail, 参数: {'id': 'TS259'}
2025-08-03 15:23:16 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 38 条记录
2025-08-03 15:23:16 | 调用API: concept_detail, 参数: {'id': 'TS260'}
2025-08-03 15:23:17 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 29 条记录
2025-08-03 15:23:17 | 调用API: concept_detail, 参数: {'id': 'TS261'}
2025-08-03 15:23:17 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:23:18 | 调用API: concept_detail, 参数: {'id': 'TS262'}
2025-08-03 15:23:18 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:23:18 | 调用API: concept_detail, 参数: {'id': 'TS263'}
2025-08-03 15:23:18 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 11 条记录
2025-08-03 15:23:19 | 调用API: concept_detail, 参数: {'id': 'TS264'}
2025-08-03 15:23:19 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:23:19 | 调用API: concept_detail, 参数: {'id': 'TS265'}
2025-08-03 15:23:19 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 43 条记录
2025-08-03 15:23:20 | 调用API: concept_detail, 参数: {'id': 'TS266'}
2025-08-03 15:23:20 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 3 条记录
2025-08-03 15:23:20 | 调用API: concept_detail, 参数: {'id': 'TS267'}
2025-08-03 15:23:20 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 33 条记录
2025-08-03 15:23:21 | 调用API: concept_detail, 参数: {'id': 'TS268'}
2025-08-03 15:23:21 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:23:22 | 调用API: concept_detail, 参数: {'id': 'TS269'}
2025-08-03 15:23:22 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:23:22 | 调用API: concept_detail, 参数: {'id': 'TS270'}
2025-08-03 15:23:22 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:23:22 | 调用API: concept_detail, 参数: {'id': 'TS271'}
2025-08-03 15:23:23 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:23:23 | 调用API: concept_detail, 参数: {'id': 'TS272'}
2025-08-03 15:23:23 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 18 条记录
2025-08-03 15:23:24 | 调用API: concept_detail, 参数: {'id': 'TS273'}
2025-08-03 15:23:24 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:23:24 | 调用API: concept_detail, 参数: {'id': 'TS274'}
2025-08-03 15:23:24 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 4 条记录
2025-08-03 15:23:24 | 调用API: concept_detail, 参数: {'id': 'TS275'}
2025-08-03 15:23:24 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 1 条记录
2025-08-03 15:23:25 | 调用API: concept_detail, 参数: {'id': 'TS276'}
2025-08-03 15:23:25 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 11 条记录
2025-08-03 15:23:25 | 调用API: concept_detail, 参数: {'id': 'TS277'}
2025-08-03 15:23:25 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 6 条记录
2025-08-03 15:23:25 | 调用API: concept_detail, 参数: {'id': 'TS278'}
2025-08-03 15:23:26 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 14 条记录
2025-08-03 15:23:26 | 调用API: concept_detail, 参数: {'id': 'TS279'}
2025-08-03 15:23:26 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 20 条记录
2025-08-03 15:23:26 | 调用API: concept_detail, 参数: {'id': 'TS280'}
2025-08-03 15:23:27 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 12 条记录
2025-08-03 15:23:27 | 调用API: concept_detail, 参数: {'id': 'TS281'}
2025-08-03 15:23:27 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 31 条记录
2025-08-03 15:23:28 | 调用API: concept_detail, 参数: {'id': 'TS282'}
2025-08-03 15:23:28 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 27 条记录
2025-08-03 15:23:29 | 调用API: concept_detail, 参数: {'id': 'TS283'}
2025-08-03 15:23:29 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:23:29 | 调用API: concept_detail, 参数: {'id': 'TS284'}
2025-08-03 15:23:29 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 25 条记录
2025-08-03 15:23:30 | 调用API: concept_detail, 参数: {'id': 'TS285'}
2025-08-03 15:23:30 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 220 条记录
2025-08-03 15:23:34 | 调用API: concept_detail, 参数: {'id': 'TS286'}
2025-08-03 15:23:35 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:23:35 | 调用API: concept_detail, 参数: {'id': 'TS287'}
2025-08-03 15:23:35 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 2 条记录
2025-08-03 15:23:35 | 调用API: concept_detail, 参数: {'id': 'TS288'}
2025-08-03 15:23:35 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 45 条记录
2025-08-03 15:23:36 | 调用API: concept_detail, 参数: {'id': 'TS289'}
2025-08-03 15:23:36 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 48 条记录
2025-08-03 15:23:37 | 调用API: concept_detail, 参数: {'id': 'TS290'}
2025-08-03 15:23:38 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 9 条记录
2025-08-03 15:23:38 | 调用API: concept_detail, 参数: {'id': 'TS291'}
2025-08-03 15:23:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:23:38 | 调用API: concept_detail, 参数: {'id': 'TS292'}
2025-08-03 15:23:38 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 24 条记录
2025-08-03 15:23:39 | 调用API: concept_detail, 参数: {'id': 'TS293'}
2025-08-03 15:23:39 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 3 条记录
2025-08-03 15:23:39 | 调用API: concept_detail, 参数: {'id': 'TS294'}
2025-08-03 15:23:39 | 调用API: concept_detail, 参数: {'id': 'TS295'}
2025-08-03 15:23:40 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 23 条记录
2025-08-03 15:23:40 | 调用API: concept_detail, 参数: {'id': 'TS296'}
2025-08-03 15:23:40 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 46 条记录
2025-08-03 15:23:41 | 调用API: concept_detail, 参数: {'id': 'TS297'}
2025-08-03 15:23:41 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 15 条记录
2025-08-03 15:23:42 | 调用API: concept_detail, 参数: {'id': 'TS298'}
2025-08-03 15:23:42 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 145 条记录
2025-08-03 15:23:45 | 调用API: concept_detail, 参数: {'id': 'TS299'}
2025-08-03 15:23:45 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 34 条记录
2025-08-03 15:23:46 | 调用API: concept_detail, 参数: {'id': 'TS300'}
2025-08-03 15:23:46 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 11 条记录
2025-08-03 15:23:47 | 调用API: concept_detail, 参数: {'id': 'TS301'}
2025-08-03 15:23:47 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 15 条记录
2025-08-03 15:23:47 | 调用API: concept_detail, 参数: {'id': 'TS302'}
2025-08-03 15:23:47 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 2 条记录
2025-08-03 15:23:48 | 调用API: concept_detail, 参数: {'id': 'TS303'}
2025-08-03 15:23:48 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 11 条记录
2025-08-03 15:23:48 | 调用API: concept_detail, 参数: {'id': 'TS304'}
2025-08-03 15:23:48 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 5 条记录
2025-08-03 15:23:49 | 调用API: concept_detail, 参数: {'id': 'TS305'}
2025-08-03 15:23:49 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:23:49 | 调用API: concept_detail, 参数: {'id': 'TS306'}
2025-08-03 15:23:49 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 20 条记录
2025-08-03 15:23:50 | 调用API: concept_detail, 参数: {'id': 'TS307'}
2025-08-03 15:23:50 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 4 条记录
2025-08-03 15:23:51 | 调用API: concept_detail, 参数: {'id': 'TS308'}
2025-08-03 15:23:51 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 6 条记录
2025-08-03 15:23:51 | 调用API: concept_detail, 参数: {'id': 'TS309'}
2025-08-03 15:23:51 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 9 条记录
2025-08-03 15:23:52 | 调用API: concept_detail, 参数: {'id': 'TS310'}
2025-08-03 15:23:52 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 16 条记录
2025-08-03 15:23:52 | 调用API: concept_detail, 参数: {'id': 'TS311'}
2025-08-03 15:23:52 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 14 条记录
2025-08-03 15:23:53 | 调用API: concept_detail, 参数: {'id': 'TS312'}
2025-08-03 15:23:53 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 5 条记录
2025-08-03 15:23:53 | 调用API: concept_detail, 参数: {'id': 'TS313'}
2025-08-03 15:23:53 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 16 条记录
2025-08-03 15:23:54 | 调用API: concept_detail, 参数: {'id': 'TS314'}
2025-08-03 15:23:54 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 30 条记录
2025-08-03 15:23:55 | 调用API: concept_detail, 参数: {'id': 'TS315'}
2025-08-03 15:23:55 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 10 条记录
2025-08-03 15:23:55 | 调用API: concept_detail, 参数: {'id': 'TS316'}
2025-08-03 15:23:55 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 180 条记录
2025-08-03 15:23:59 | 调用API: concept_detail, 参数: {'id': 'TS317'}
2025-08-03 15:23:59 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 64 条记录
2025-08-03 15:24:01 | 调用API: concept_detail, 参数: {'id': 'TS318'}
2025-08-03 15:24:01 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 16 条记录
2025-08-03 15:24:02 | 调用API: concept_detail, 参数: {'id': 'TS319'}
2025-08-03 15:24:02 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 30 条记录
2025-08-03 15:24:03 | 调用API: concept_detail, 参数: {'id': 'TS320'}
2025-08-03 15:24:03 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 12 条记录
2025-08-03 15:24:04 | 调用API: concept_detail, 参数: {'id': 'TS321'}
2025-08-03 15:24:04 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 5 条记录
2025-08-03 15:24:04 | 调用API: concept_detail, 参数: {'id': 'TS322'}
2025-08-03 15:24:04 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 169 条记录
2025-08-03 15:24:08 | 调用API: concept_detail, 参数: {'id': 'TS323'}
2025-08-03 15:24:08 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 43 条记录
2025-08-03 15:24:09 | 调用API: concept_detail, 参数: {'id': 'TS324'}
2025-08-03 15:24:10 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 48 条记录
2025-08-03 15:24:11 | 调用API: concept_detail, 参数: {'id': 'TS325'}
2025-08-03 15:24:11 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 21 条记录
2025-08-03 15:24:11 | 调用API: concept_detail, 参数: {'id': 'TS326'}
2025-08-03 15:24:11 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 8 条记录
2025-08-03 15:24:12 | 调用API: concept_detail, 参数: {'id': 'TS327'}
2025-08-03 15:24:12 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 19 条记录
2025-08-03 15:24:12 | 调用API: concept_detail, 参数: {'id': 'TS328'}
2025-08-03 15:24:13 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 19 条记录
2025-08-03 15:24:13 | 调用API: concept_detail, 参数: {'id': 'TS329'}
2025-08-03 15:24:13 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 7 条记录
2025-08-03 15:24:14 | 调用API: concept_detail, 参数: {'id': 'TS330'}
2025-08-03 15:24:14 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 125 条记录
2025-08-03 15:24:16 | 调用API: concept_detail, 参数: {'id': 'TS331'}
2025-08-03 15:24:16 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:24:17 | 调用API: concept_detail, 参数: {'id': 'TS332'}
2025-08-03 15:24:17 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 37 条记录
2025-08-03 15:24:18 | 调用API: concept_detail, 参数: {'id': 'TS333'}
2025-08-03 15:24:18 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 16 条记录
2025-08-03 15:24:18 | 调用API: concept_detail, 参数: {'id': 'TS334'}
2025-08-03 15:24:18 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 75 条记录
2025-08-03 15:24:20 | 调用API: concept_detail, 参数: {'id': 'TS335'}
2025-08-03 15:24:20 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:24:20 | 调用API: concept_detail, 参数: {'id': 'TS336'}
2025-08-03 15:24:20 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:24:21 | 调用API: concept_detail, 参数: {'id': 'TS337'}
2025-08-03 15:24:21 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 20 条记录
2025-08-03 15:24:22 | 调用API: concept_detail, 参数: {'id': 'TS338'}
2025-08-03 15:24:22 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 21 条记录
2025-08-03 15:24:23 | 调用API: concept_detail, 参数: {'id': 'TS339'}
2025-08-03 15:24:23 | API调用成功: concept_detail, 耗时: 0.23秒, 返回 7 条记录
2025-08-03 15:24:23 | 调用API: concept_detail, 参数: {'id': 'TS340'}
2025-08-03 15:24:23 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 6 条记录
2025-08-03 15:24:24 | 调用API: concept_detail, 参数: {'id': 'TS341'}
2025-08-03 15:24:24 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 12 条记录
2025-08-03 15:24:24 | 调用API: concept_detail, 参数: {'id': 'TS342'}
2025-08-03 15:24:24 | API调用成功: concept_detail, 耗时: 0.23秒, 返回 6 条记录
2025-08-03 15:24:25 | 调用API: concept_detail, 参数: {'id': 'TS343'}
2025-08-03 15:24:25 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 14 条记录
2025-08-03 15:24:25 | 调用API: concept_detail, 参数: {'id': 'TS344'}
2025-08-03 15:24:25 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 21 条记录
2025-08-03 15:24:26 | 调用API: concept_detail, 参数: {'id': 'TS345'}
2025-08-03 15:24:26 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 5 条记录
2025-08-03 15:24:26 | 调用API: concept_detail, 参数: {'id': 'TS346'}
2025-08-03 15:24:26 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1 条记录
2025-08-03 15:24:27 | 调用API: concept_detail, 参数: {'id': 'TS347'}
2025-08-03 15:24:27 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 2 条记录
2025-08-03 15:24:27 | 调用API: concept_detail, 参数: {'id': 'TS348'}
2025-08-03 15:24:27 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:24:27 | 调用API: concept_detail, 参数: {'id': 'TS349'}
2025-08-03 15:24:27 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 2 条记录
2025-08-03 15:24:28 | 调用API: concept_detail, 参数: {'id': 'TS350'}
2025-08-03 15:24:28 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 28 条记录
2025-08-03 15:24:29 | 调用API: concept_detail, 参数: {'id': 'TS351'}
2025-08-03 15:24:29 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 15 条记录
2025-08-03 15:24:29 | 调用API: concept_detail, 参数: {'id': 'TS352'}
2025-08-03 15:24:29 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 6 条记录
2025-08-03 15:24:30 | 调用API: concept_detail, 参数: {'id': 'TS353'}
2025-08-03 15:24:30 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 5 条记录
2025-08-03 15:24:30 | 调用API: concept_detail, 参数: {'id': 'TS354'}
2025-08-03 15:24:30 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 885 条记录
2025-08-03 15:24:51 | 调用API: concept_detail, 参数: {'id': 'TS355'}
2025-08-03 15:24:51 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 27 条记录
2025-08-03 15:24:52 | 调用API: concept_detail, 参数: {'id': 'TS356'}
2025-08-03 15:24:52 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 5 条记录
2025-08-03 15:24:53 | 调用API: concept_detail, 参数: {'id': 'TS357'}
2025-08-03 15:24:53 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 5 条记录
2025-08-03 15:24:53 | 调用API: concept_detail, 参数: {'id': 'TS358'}
2025-08-03 15:24:53 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:24:53 | 调用API: concept_detail, 参数: {'id': 'TS359'}
2025-08-03 15:24:53 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 16 条记录
2025-08-03 15:24:54 | 调用API: concept_detail, 参数: {'id': 'TS360'}
2025-08-03 15:24:54 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 2 条记录
2025-08-03 15:24:54 | 调用API: concept_detail, 参数: {'id': 'TS361'}
2025-08-03 15:24:54 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 9 条记录
2025-08-03 15:24:55 | 调用API: concept_detail, 参数: {'id': 'TS362'}
2025-08-03 15:24:55 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 19 条记录
2025-08-03 15:24:55 | 调用API: concept_detail, 参数: {'id': 'TS363'}
2025-08-03 15:24:55 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 92 条记录
2025-08-03 15:24:57 | 调用API: concept_detail, 参数: {'id': 'TS364'}
2025-08-03 15:24:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 44 条记录
2025-08-03 15:24:58 | 调用API: concept_detail, 参数: {'id': 'TS365'}
2025-08-03 15:24:59 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 9 条记录
2025-08-03 15:24:59 | 调用API: concept_detail, 参数: {'id': 'TS366'}
2025-08-03 15:24:59 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 31 条记录
2025-08-03 15:25:00 | 调用API: concept_detail, 参数: {'id': 'TS367'}
2025-08-03 15:25:00 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 33 条记录
2025-08-03 15:25:01 | 调用API: concept_detail, 参数: {'id': 'TS368'}
2025-08-03 15:25:01 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:25:01 | 调用API: concept_detail, 参数: {'id': 'TS369'}
2025-08-03 15:25:01 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:25:02 | 调用API: concept_detail, 参数: {'id': 'TS370'}
2025-08-03 15:25:02 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 31 条记录
2025-08-03 15:25:03 | 调用API: concept_detail, 参数: {'id': 'TS371'}
2025-08-03 15:25:03 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 24 条记录
2025-08-03 15:25:03 | 调用API: concept_detail, 参数: {'id': 'TS372'}
2025-08-03 15:25:03 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 42 条记录
2025-08-03 15:25:04 | 调用API: concept_detail, 参数: {'id': 'TS373'}
2025-08-03 15:25:05 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 19 条记录
2025-08-03 15:25:05 | 调用API: concept_detail, 参数: {'id': 'TS374'}
2025-08-03 15:25:05 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 17 条记录
2025-08-03 15:25:06 | 调用API: concept_detail, 参数: {'id': 'TS375'}
2025-08-03 15:25:06 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 41 条记录
2025-08-03 15:25:07 | 调用API: concept_detail, 参数: {'id': 'TS376'}
2025-08-03 15:25:07 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 60 条记录
2025-08-03 15:25:08 | 调用API: concept_detail, 参数: {'id': 'TS377'}
2025-08-03 15:25:09 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:25:09 | 调用API: concept_detail, 参数: {'id': 'TS378'}
2025-08-03 15:25:09 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 2 条记录
2025-08-03 15:25:09 | 调用API: concept_detail, 参数: {'id': 'TS379'}
2025-08-03 15:25:09 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 77 条记录
2025-08-03 15:25:11 | 调用API: concept_detail, 参数: {'id': 'TS380'}
2025-08-03 15:25:11 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:25:12 | 调用API: concept_detail, 参数: {'id': 'TS381'}
2025-08-03 15:25:12 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:25:12 | 调用API: concept_detail, 参数: {'id': 'TS382'}
2025-08-03 15:25:12 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 8 条记录
2025-08-03 15:25:12 | 调用API: concept_detail, 参数: {'id': 'TS383'}
2025-08-03 15:25:13 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 5 条记录
2025-08-03 15:25:13 | 调用API: concept_detail, 参数: {'id': 'TS384'}
2025-08-03 15:25:13 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 24 条记录
2025-08-03 15:25:14 | 调用API: concept_detail, 参数: {'id': 'TS385'}
2025-08-03 15:25:14 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 11 条记录
2025-08-03 15:25:14 | 调用API: concept_detail, 参数: {'id': 'TS386'}
2025-08-03 15:25:14 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 27 条记录
2025-08-03 15:25:15 | 调用API: concept_detail, 参数: {'id': 'TS387'}
2025-08-03 15:25:15 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 15 条记录
2025-08-03 15:25:16 | 调用API: concept_detail, 参数: {'id': 'TS388'}
2025-08-03 15:25:16 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 22 条记录
2025-08-03 15:25:16 | 调用API: concept_detail, 参数: {'id': 'TS389'}
2025-08-03 15:25:17 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 12 条记录
2025-08-03 15:25:17 | 调用API: concept_detail, 参数: {'id': 'TS390'}
2025-08-03 15:25:17 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 8 条记录
2025-08-03 15:25:17 | 调用API: concept_detail, 参数: {'id': 'TS391'}
2025-08-03 15:25:17 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 18 条记录
2025-08-03 15:25:18 | 调用API: concept_detail, 参数: {'id': 'TS392'}
2025-08-03 15:25:18 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 586 条记录
2025-08-03 15:25:32 | 调用API: concept_detail, 参数: {'id': 'TS393'}
2025-08-03 15:25:32 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 10 条记录
2025-08-03 15:25:33 | 调用API: concept_detail, 参数: {'id': 'TS394'}
2025-08-03 15:25:33 | 调用API: concept_detail, 参数: {'id': 'TS395'}
2025-08-03 15:25:33 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:25:33 | 调用API: concept_detail, 参数: {'id': 'TS396'}
2025-08-03 15:25:33 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 101 条记录
2025-08-03 15:25:36 | 调用API: concept_detail, 参数: {'id': 'TS397'}
2025-08-03 15:25:36 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 13 条记录
2025-08-03 15:25:36 | 调用API: concept_detail, 参数: {'id': 'TS398'}
2025-08-03 15:25:36 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 54 条记录
2025-08-03 15:25:38 | 调用API: concept_detail, 参数: {'id': 'TS399'}
2025-08-03 15:25:38 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 11 条记录
2025-08-03 15:25:38 | 调用API: concept_detail, 参数: {'id': 'TS400'}
2025-08-03 15:25:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 126 条记录
2025-08-03 15:25:41 | 调用API: concept_detail, 参数: {'id': 'TS401'}
2025-08-03 15:25:41 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 41 条记录
2025-08-03 15:25:43 | 调用API: concept_detail, 参数: {'id': 'TS402'}
2025-08-03 15:25:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 5 条记录
2025-08-03 15:25:43 | 调用API: concept_detail, 参数: {'id': 'TS403'}
2025-08-03 15:25:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 32 条记录
2025-08-03 15:25:44 | 调用API: concept_detail, 参数: {'id': 'TS404'}
2025-08-03 15:25:44 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 8 条记录
2025-08-03 15:25:45 | 调用API: concept_detail, 参数: {'id': 'TS405'}
2025-08-03 15:25:45 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 45 条记录
2025-08-03 15:25:46 | 调用API: concept_detail, 参数: {'id': 'TS406'}
2025-08-03 15:25:46 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 10 条记录
2025-08-03 15:25:47 | 调用API: concept_detail, 参数: {'id': 'TS407'}
2025-08-03 15:25:47 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 33 条记录
2025-08-03 15:25:47 | 调用API: concept_detail, 参数: {'id': 'TS408'}
2025-08-03 15:25:48 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:25:48 | 调用API: concept_detail, 参数: {'id': 'TS409'}
2025-08-03 15:25:48 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:25:48 | 调用API: concept_detail, 参数: {'id': 'TS410'}
2025-08-03 15:25:48 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 88 条记录
2025-08-03 15:25:50 | 调用API: concept_detail, 参数: {'id': 'TS411'}
2025-08-03 15:25:51 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 15 条记录
2025-08-03 15:25:51 | 调用API: concept_detail, 参数: {'id': 'TS412'}
2025-08-03 15:25:51 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 18 条记录
2025-08-03 15:25:52 | 调用API: concept_detail, 参数: {'id': 'TS413'}
2025-08-03 15:25:52 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:25:52 | 调用API: concept_detail, 参数: {'id': 'TS414'}
2025-08-03 15:25:52 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 50 条记录
2025-08-03 15:25:54 | 调用API: concept_detail, 参数: {'id': 'TS415'}
2025-08-03 15:25:54 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:25:54 | 调用API: concept_detail, 参数: {'id': 'TS416'}
2025-08-03 15:25:54 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 2 条记录
2025-08-03 15:25:55 | 调用API: concept_detail, 参数: {'id': 'TS417'}
2025-08-03 15:25:55 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 7 条记录
2025-08-03 15:25:55 | 调用API: concept_detail, 参数: {'id': 'TS418'}
2025-08-03 15:25:55 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 26 条记录
2025-08-03 15:25:56 | 调用API: concept_detail, 参数: {'id': 'TS419'}
2025-08-03 15:25:56 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 5 条记录
2025-08-03 15:25:56 | 调用API: concept_detail, 参数: {'id': 'TS420'}
2025-08-03 15:25:57 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1 条记录
2025-08-03 15:25:57 | 调用API: concept_detail, 参数: {'id': 'TS421'}
2025-08-03 15:25:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:25:57 | 调用API: concept_detail, 参数: {'id': 'TS422'}
2025-08-03 15:25:57 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 49 条记录
2025-08-03 15:25:59 | 调用API: concept_detail, 参数: {'id': 'TS423'}
2025-08-03 15:25:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:26:00 | 调用API: concept_detail, 参数: {'id': 'TS424'}
2025-08-03 15:26:00 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 108 条记录
2025-08-03 15:26:08 | 调用API: concept_detail, 参数: {'id': 'TS425'}
2025-08-03 15:26:08 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 193 条记录
2025-08-03 15:26:18 | 调用API: concept_detail, 参数: {'id': 'TS426'}
2025-08-03 15:26:18 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 1000 条记录
2025-08-03 15:26:54 | 调用API: concept_detail, 参数: {'id': 'TS427'}
2025-08-03 15:26:54 | API调用成功: concept_detail, 耗时: 0.28秒, 返回 3 条记录
2025-08-03 15:26:55 | 调用API: concept_detail, 参数: {'id': 'TS428'}
2025-08-03 15:26:55 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 71 条记录
2025-08-03 15:26:57 | 调用API: concept_detail, 参数: {'id': 'TS429'}
2025-08-03 15:26:57 | API调用成功: concept_detail, 耗时: 0.22秒, 返回 14 条记录
2025-08-03 15:26:58 | 调用API: concept_detail, 参数: {'id': 'TS430'}
2025-08-03 15:26:58 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 13 条记录
2025-08-03 15:26:59 | 调用API: concept_detail, 参数: {'id': 'TS431'}
2025-08-03 15:26:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:26:59 | 调用API: concept_detail, 参数: {'id': 'TS432'}
2025-08-03 15:26:59 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 7 条记录
2025-08-03 15:27:00 | 调用API: concept_detail, 参数: {'id': 'TS433'}
2025-08-03 15:27:00 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 13 条记录
2025-08-03 15:27:00 | 调用API: concept_detail, 参数: {'id': 'TS434'}
2025-08-03 15:27:01 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 36 条记录
2025-08-03 15:27:02 | 调用API: concept_detail, 参数: {'id': 'TS435'}
2025-08-03 15:27:02 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 15 条记录
2025-08-03 15:27:02 | 调用API: concept_detail, 参数: {'id': 'TS436'}
2025-08-03 15:27:03 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 277 条记录
2025-08-03 15:27:11 | 调用API: concept_detail, 参数: {'id': 'TS437'}
2025-08-03 15:27:11 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:27:12 | 调用API: concept_detail, 参数: {'id': 'TS438'}
2025-08-03 15:27:12 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 38 条记录
2025-08-03 15:27:13 | 调用API: concept_detail, 参数: {'id': 'TS439'}
2025-08-03 15:27:13 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:27:13 | 调用API: concept_detail, 参数: {'id': 'TS440'}
2025-08-03 15:27:14 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 1 条记录
2025-08-03 15:27:14 | 调用API: concept_detail, 参数: {'id': 'TS441'}
2025-08-03 15:27:14 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 67 条记录
2025-08-03 15:27:16 | 调用API: concept_detail, 参数: {'id': 'TS442'}
2025-08-03 15:27:16 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 36 条记录
2025-08-03 15:27:17 | 调用API: concept_detail, 参数: {'id': 'TS443'}
2025-08-03 15:27:18 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 19 条记录
2025-08-03 15:27:18 | 调用API: concept_detail, 参数: {'id': 'TS444'}
2025-08-03 15:27:18 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 5 条记录
2025-08-03 15:27:19 | 调用API: concept_detail, 参数: {'id': 'TS445'}
2025-08-03 15:27:19 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 20 条记录
2025-08-03 15:27:20 | 调用API: concept_detail, 参数: {'id': 'TS446'}
2025-08-03 15:27:20 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 3 条记录
2025-08-03 15:27:20 | 调用API: concept_detail, 参数: {'id': 'TS447'}
2025-08-03 15:27:20 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 13 条记录
2025-08-03 15:27:21 | 调用API: concept_detail, 参数: {'id': 'TS448'}
2025-08-03 15:27:21 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 72 条记录
2025-08-03 15:27:23 | 调用API: concept_detail, 参数: {'id': 'TS449'}
2025-08-03 15:27:23 | API调用成功: concept_detail, 耗时: 0.23秒, 返回 7 条记录
2025-08-03 15:27:23 | 调用API: concept_detail, 参数: {'id': 'TS450'}
2025-08-03 15:27:24 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 19 条记录
2025-08-03 15:27:24 | 调用API: concept_detail, 参数: {'id': 'TS451'}
2025-08-03 15:27:24 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 126 条记录
2025-08-03 15:27:29 | 调用API: concept_detail, 参数: {'id': 'TS452'}
2025-08-03 15:27:29 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 52 条记录
2025-08-03 15:27:31 | 调用API: concept_detail, 参数: {'id': 'TS453'}
2025-08-03 15:27:31 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 36 条记录
2025-08-03 15:27:32 | 调用API: concept_detail, 参数: {'id': 'TS454'}
2025-08-03 15:27:32 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:27:34 | 调用API: concept_detail, 参数: {'id': 'TS455'}
2025-08-03 15:27:34 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 14 条记录
2025-08-03 15:27:35 | 调用API: concept_detail, 参数: {'id': 'TS456'}
2025-08-03 15:27:35 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 99 条记录
2025-08-03 15:27:39 | 调用API: concept_detail, 参数: {'id': 'TS457'}
2025-08-03 15:27:39 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 50 条记录
2025-08-03 15:27:40 | 调用API: concept_detail, 参数: {'id': 'TS458'}
2025-08-03 15:27:41 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 60 条记录
2025-08-03 15:27:43 | 调用API: concept_detail, 参数: {'id': 'TS459'}
2025-08-03 15:27:44 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 19 条记录
2025-08-03 15:27:44 | 调用API: concept_detail, 参数: {'id': 'TS460'}
2025-08-03 15:27:44 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 173 条记录
2025-08-03 15:27:50 | 调用API: concept_detail, 参数: {'id': 'TS461'}
2025-08-03 15:27:50 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 16 条记录
2025-08-03 15:27:51 | 调用API: concept_detail, 参数: {'id': 'TS462'}
2025-08-03 15:27:51 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 19 条记录
2025-08-03 15:27:53 | 调用API: concept_detail, 参数: {'id': 'TS463'}
2025-08-03 15:27:53 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 4 条记录
2025-08-03 15:27:53 | 调用API: concept_detail, 参数: {'id': 'TS464'}
2025-08-03 15:27:53 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 18 条记录
2025-08-03 15:27:54 | 调用API: concept_detail, 参数: {'id': 'TS465'}
2025-08-03 15:27:54 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:27:54 | 调用API: concept_detail, 参数: {'id': 'TS466'}
2025-08-03 15:27:55 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 76 条记录
2025-08-03 15:27:57 | 调用API: concept_detail, 参数: {'id': 'TS467'}
2025-08-03 15:27:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 32 条记录
2025-08-03 15:27:58 | 调用API: concept_detail, 参数: {'id': 'TS468'}
2025-08-03 15:27:58 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 4 条记录
2025-08-03 15:27:58 | 调用API: concept_detail, 参数: {'id': 'TS469'}
2025-08-03 15:27:58 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 76 条记录
2025-08-03 15:28:01 | 调用API: concept_detail, 参数: {'id': 'TS470'}
2025-08-03 15:28:01 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 16 条记录
2025-08-03 15:28:02 | 调用API: concept_detail, 参数: {'id': 'TS471'}
2025-08-03 15:28:02 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:28:03 | 调用API: concept_detail, 参数: {'id': 'TS472'}
2025-08-03 15:28:03 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 51 条记录
2025-08-03 15:28:05 | 调用API: concept_detail, 参数: {'id': 'TS473'}
2025-08-03 15:28:05 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 13 条记录
2025-08-03 15:28:06 | 调用API: concept_detail, 参数: {'id': 'TS474'}
2025-08-03 15:28:06 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 33 条记录
2025-08-03 15:28:07 | 调用API: concept_detail, 参数: {'id': 'TS475'}
2025-08-03 15:28:07 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 144 条记录
2025-08-03 15:28:12 | 调用API: concept_detail, 参数: {'id': 'TS476'}
2025-08-03 15:28:12 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 415 条记录
2025-08-03 15:28:27 | 调用API: concept_detail, 参数: {'id': 'TS477'}
2025-08-03 15:28:27 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:28:27 | 调用API: concept_detail, 参数: {'id': 'TS478'}
2025-08-03 15:28:27 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 37 条记录
2025-08-03 15:28:29 | 调用API: concept_detail, 参数: {'id': 'TS479'}
2025-08-03 15:28:29 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:28:29 | 调用API: concept_detail, 参数: {'id': 'TS480'}
2025-08-03 15:28:29 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 9 条记录
2025-08-03 15:28:30 | 调用API: concept_detail, 参数: {'id': 'TS481'}
2025-08-03 15:28:30 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 213 条记录
2025-08-03 15:28:38 | 调用API: concept_detail, 参数: {'id': 'TS482'}
2025-08-03 15:28:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 79 条记录
2025-08-03 15:28:42 | 调用API: concept_detail, 参数: {'id': 'TS483'}
2025-08-03 15:28:42 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 38 条记录
2025-08-03 15:28:45 | 调用API: concept_detail, 参数: {'id': 'TS484'}
2025-08-03 15:28:45 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 48 条记录
2025-08-03 15:28:47 | 调用API: concept_detail, 参数: {'id': 'TS485'}
2025-08-03 15:28:48 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 5 条记录
2025-08-03 15:28:49 | 调用API: concept_detail, 参数: {'id': 'TS486'}
2025-08-03 15:28:49 | API调用成功: concept_detail, 耗时: 0.29秒, 返回 17 条记录
2025-08-03 15:28:50 | 调用API: concept_detail, 参数: {'id': 'TS487'}
2025-08-03 15:28:50 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 14 条记录
2025-08-03 15:28:52 | 调用API: concept_detail, 参数: {'id': 'TS488'}
2025-08-03 15:28:52 | API调用成功: concept_detail, 耗时: 0.32秒, 返回 103 条记录
2025-08-03 15:28:56 | 调用API: concept_detail, 参数: {'id': 'TS489'}
2025-08-03 15:28:56 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 6 条记录
2025-08-03 15:28:57 | 调用API: concept_detail, 参数: {'id': 'TS490'}
2025-08-03 15:28:57 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 40 条记录
2025-08-03 15:28:59 | 调用API: concept_detail, 参数: {'id': 'TS491'}
2025-08-03 15:29:00 | API调用成功: concept_detail, 耗时: 0.24秒, 返回 3 条记录
2025-08-03 15:29:01 | 调用API: concept_detail, 参数: {'id': 'TS492'}
2025-08-03 15:29:01 | API调用成功: concept_detail, 耗时: 0.37秒, 返回 21 条记录
2025-08-03 15:29:02 | 调用API: concept_detail, 参数: {'id': 'TS493'}
2025-08-03 15:29:03 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 20 条记录
2025-08-03 15:29:03 | 调用API: concept_detail, 参数: {'id': 'TS494'}
2025-08-03 15:29:04 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 24 条记录
2025-08-03 15:29:06 | 调用API: concept_detail, 参数: {'id': 'TS495'}
2025-08-03 15:29:06 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 36 条记录
2025-08-03 15:29:09 | 调用API: concept_detail, 参数: {'id': 'TS496'}
2025-08-03 15:29:09 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 19 条记录
2025-08-03 15:29:10 | 调用API: concept_detail, 参数: {'id': 'TS497'}
2025-08-03 15:29:10 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:29:10 | 调用API: concept_detail, 参数: {'id': 'TS498'}
2025-08-03 15:29:10 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:29:11 | 调用API: concept_detail, 参数: {'id': 'TS499'}
2025-08-03 15:29:11 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 10 条记录
2025-08-03 15:29:12 | 调用API: concept_detail, 参数: {'id': 'TS500'}
2025-08-03 15:29:12 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 64 条记录
2025-08-03 15:29:15 | 调用API: concept_detail, 参数: {'id': 'TS501'}
2025-08-03 15:29:15 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 4 条记录
2025-08-03 15:29:16 | 调用API: concept_detail, 参数: {'id': 'TS502'}
2025-08-03 15:29:16 | API调用成功: concept_detail, 耗时: 0.22秒, 返回 11 条记录
2025-08-03 15:29:16 | 调用API: concept_detail, 参数: {'id': 'TS503'}
2025-08-03 15:29:17 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 3 条记录
2025-08-03 15:29:17 | 调用API: concept_detail, 参数: {'id': 'TS504'}
2025-08-03 15:29:17 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 15 条记录
2025-08-03 15:29:18 | 调用API: concept_detail, 参数: {'id': 'TS505'}
2025-08-03 15:29:18 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:29:19 | 调用API: concept_detail, 参数: {'id': 'TS506'}
2025-08-03 15:29:19 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 6 条记录
2025-08-03 15:29:19 | 调用API: concept_detail, 参数: {'id': 'TS507'}
2025-08-03 15:29:20 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 18 条记录
2025-08-03 15:29:20 | 调用API: concept_detail, 参数: {'id': 'TS508'}
2025-08-03 15:29:20 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 38 条记录
2025-08-03 15:29:22 | 调用API: concept_detail, 参数: {'id': 'TS509'}
2025-08-03 15:29:22 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 87 条记录
2025-08-03 15:29:26 | 调用API: concept_detail, 参数: {'id': 'TS510'}
2025-08-03 15:29:26 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 10 条记录
2025-08-03 15:29:27 | 调用API: concept_detail, 参数: {'id': 'TS511'}
2025-08-03 15:29:27 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:29:27 | 调用API: concept_detail, 参数: {'id': 'TS512'}
2025-08-03 15:29:27 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 60 条记录
2025-08-03 15:29:29 | 调用API: concept_detail, 参数: {'id': 'TS513'}
2025-08-03 15:29:30 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 9 条记录
2025-08-03 15:29:30 | 调用API: concept_detail, 参数: {'id': 'TS514'}
2025-08-03 15:29:30 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 62 条记录
2025-08-03 15:29:32 | 调用API: concept_detail, 参数: {'id': 'TS515'}
2025-08-03 15:29:33 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 6 条记录
2025-08-03 15:29:33 | 调用API: concept_detail, 参数: {'id': 'TS516'}
2025-08-03 15:29:33 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 4 条记录
2025-08-03 15:29:33 | 调用API: concept_detail, 参数: {'id': 'TS517'}
2025-08-03 15:29:33 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:29:34 | 调用API: concept_detail, 参数: {'id': 'TS518'}
2025-08-03 15:29:34 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 28 条记录
2025-08-03 15:29:35 | 调用API: concept_detail, 参数: {'id': 'TS519'}
2025-08-03 15:29:35 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 12 条记录
2025-08-03 15:29:36 | 调用API: concept_detail, 参数: {'id': 'TS520'}
2025-08-03 15:29:36 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 13 条记录
2025-08-03 15:29:37 | 调用API: concept_detail, 参数: {'id': 'TS521'}
2025-08-03 15:29:37 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 20 条记录
2025-08-03 15:29:38 | 调用API: concept_detail, 参数: {'id': 'TS522'}
2025-08-03 15:29:38 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 3 条记录
2025-08-03 15:29:38 | 调用API: concept_detail, 参数: {'id': 'TS523'}
2025-08-03 15:29:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:29:38 | 调用API: concept_detail, 参数: {'id': 'TS524'}
2025-08-03 15:29:39 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:29:39 | 调用API: concept_detail, 参数: {'id': 'TS525'}
2025-08-03 15:29:39 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 5 条记录
2025-08-03 15:29:39 | 调用API: concept_detail, 参数: {'id': 'TS526'}
2025-08-03 15:29:40 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 20 条记录
2025-08-03 15:29:40 | 调用API: concept_detail, 参数: {'id': 'TS527'}
2025-08-03 15:29:40 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 59 条记录
2025-08-03 15:29:42 | 调用API: concept_detail, 参数: {'id': 'TS528'}
2025-08-03 15:29:42 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 37 条记录
2025-08-03 15:29:44 | 调用API: concept_detail, 参数: {'id': 'TS529'}
2025-08-03 15:29:44 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:29:44 | 调用API: concept_detail, 参数: {'id': 'TS530'}
2025-08-03 15:29:44 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 30 条记录
2025-08-03 15:29:45 | 调用API: concept_detail, 参数: {'id': 'TS531'}
2025-08-03 15:29:45 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 8 条记录
2025-08-03 15:29:46 | 调用API: concept_detail, 参数: {'id': 'TS532'}
2025-08-03 15:29:46 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 20 条记录
2025-08-03 15:29:47 | 调用API: concept_detail, 参数: {'id': 'TS533'}
2025-08-03 15:29:47 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 51 条记录
2025-08-03 15:29:49 | 调用API: concept_detail, 参数: {'id': 'TS534'}
2025-08-03 15:29:50 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 12 条记录
2025-08-03 15:29:50 | 调用API: concept_detail, 参数: {'id': 'TS535'}
2025-08-03 15:29:50 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 90 条记录
2025-08-03 15:29:53 | 调用API: concept_detail, 参数: {'id': 'TS536'}
2025-08-03 15:29:54 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 19 条记录
2025-08-03 15:29:54 | 调用API: concept_detail, 参数: {'id': 'TS537'}
2025-08-03 15:29:54 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 587 条记录
2025-08-03 15:30:15 | 调用API: concept_detail, 参数: {'id': 'TS538'}
2025-08-03 15:30:15 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 22 条记录
2025-08-03 15:30:15 | 调用API: concept_detail, 参数: {'id': 'TS539'}
2025-08-03 15:30:15 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:30:16 | 调用API: concept_detail, 参数: {'id': 'TS540'}
2025-08-03 15:30:16 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 12 条记录
2025-08-03 15:30:16 | 调用API: concept_detail, 参数: {'id': 'TS541'}
2025-08-03 15:30:16 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 6 条记录
2025-08-03 15:30:17 | 调用API: concept_detail, 参数: {'id': 'TS542'}
2025-08-03 15:30:17 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 12 条记录
2025-08-03 15:30:17 | 调用API: concept_detail, 参数: {'id': 'TS543'}
2025-08-03 15:30:18 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 87 条记录
2025-08-03 15:30:21 | 调用API: concept_detail, 参数: {'id': 'TS544'}
2025-08-03 15:30:21 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 55 条记录
2025-08-03 15:30:23 | 调用API: concept_detail, 参数: {'id': 'TS545'}
2025-08-03 15:30:23 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 121 条记录
2025-08-03 15:30:27 | 调用API: concept_detail, 参数: {'id': 'TS546'}
2025-08-03 15:30:27 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 171 条记录
2025-08-03 15:30:32 | 调用API: concept_detail, 参数: {'id': 'TS547'}
2025-08-03 15:30:32 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 16 条记录
2025-08-03 15:30:33 | 调用API: concept_detail, 参数: {'id': 'TS548'}
2025-08-03 15:30:33 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 43 条记录
2025-08-03 15:30:34 | 调用API: concept_detail, 参数: {'id': 'TS549'}
2025-08-03 15:30:34 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 6 条记录
2025-08-03 15:30:35 | 调用API: concept_detail, 参数: {'id': 'TS550'}
2025-08-03 15:30:35 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 18 条记录
2025-08-03 15:30:35 | 调用API: concept_detail, 参数: {'id': 'TS551'}
2025-08-03 15:30:35 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 19 条记录
2025-08-03 15:30:36 | 调用API: concept_detail, 参数: {'id': 'TS552'}
2025-08-03 15:30:36 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 22 条记录
2025-08-03 15:30:37 | 调用API: concept_detail, 参数: {'id': 'TS553'}
2025-08-03 15:30:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 99 条记录
2025-08-03 15:30:39 | 调用API: concept_detail, 参数: {'id': 'TS554'}
2025-08-03 15:30:40 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 91 条记录
2025-08-03 15:30:42 | 调用API: concept_detail, 参数: {'id': 'TS555'}
2025-08-03 15:30:42 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 3 条记录
2025-08-03 15:30:42 | 调用API: concept_detail, 参数: {'id': 'TS556'}
2025-08-03 15:30:42 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:30:43 | 调用API: concept_detail, 参数: {'id': 'TS557'}
2025-08-03 15:30:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 7 条记录
2025-08-03 15:30:43 | 调用API: concept_detail, 参数: {'id': 'TS558'}
2025-08-03 15:30:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 46 条记录
2025-08-03 15:30:46 | 调用API: concept_detail, 参数: {'id': 'TS559'}
2025-08-03 15:30:46 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 3 条记录
2025-08-03 15:30:46 | 调用API: concept_detail, 参数: {'id': 'TS560'}
2025-08-03 15:30:46 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 7 条记录
2025-08-03 15:30:47 | 调用API: concept_detail, 参数: {'id': 'TS561'}
2025-08-03 15:30:47 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 28 条记录
2025-08-03 15:30:48 | 调用API: concept_detail, 参数: {'id': 'TS562'}
2025-08-03 15:30:48 | API调用成功: concept_detail, 耗时: 0.22秒, 返回 54 条记录
2025-08-03 15:30:50 | 调用API: concept_detail, 参数: {'id': 'TS563'}
2025-08-03 15:30:50 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:30:50 | 调用API: concept_detail, 参数: {'id': 'TS564'}
2025-08-03 15:30:50 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 25 条记录
2025-08-03 15:30:51 | 调用API: concept_detail, 参数: {'id': 'TS565'}
2025-08-03 15:30:51 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:30:51 | 调用API: concept_detail, 参数: {'id': 'TS566'}
2025-08-03 15:30:52 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 5 条记录
2025-08-03 15:30:52 | 调用API: concept_detail, 参数: {'id': 'TS567'}
2025-08-03 15:30:52 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 17 条记录
2025-08-03 15:30:52 | 调用API: concept_detail, 参数: {'id': 'TS568'}
2025-08-03 15:30:53 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 76 条记录
2025-08-03 15:30:56 | 调用API: concept_detail, 参数: {'id': 'TS569'}
2025-08-03 15:30:56 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 8 条记录
2025-08-03 15:30:57 | 调用API: concept_detail, 参数: {'id': 'TS570'}
2025-08-03 15:30:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 26 条记录
2025-08-03 15:30:58 | 调用API: concept_detail, 参数: {'id': 'TS571'}
2025-08-03 15:30:58 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 101 条记录
2025-08-03 15:31:01 | 调用API: concept_detail, 参数: {'id': 'TS572'}
2025-08-03 15:31:01 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 9 条记录
2025-08-03 15:31:02 | 调用API: concept_detail, 参数: {'id': 'TS573'}
2025-08-03 15:31:02 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 15 条记录
2025-08-03 15:31:03 | 调用API: concept_detail, 参数: {'id': 'TS574'}
2025-08-03 15:31:03 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:31:03 | 调用API: concept_detail, 参数: {'id': 'TS575'}
2025-08-03 15:31:03 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 78 条记录
2025-08-03 15:31:05 | 调用API: concept_detail, 参数: {'id': 'TS576'}
2025-08-03 15:31:06 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:31:06 | 调用API: concept_detail, 参数: {'id': 'TS577'}
2025-08-03 15:31:06 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:31:07 | 调用API: concept_detail, 参数: {'id': 'TS578'}
2025-08-03 15:31:07 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:31:07 | 调用API: concept_detail, 参数: {'id': 'TS579'}
2025-08-03 15:31:07 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 104 条记录
2025-08-03 15:31:10 | 调用API: concept_detail, 参数: {'id': 'TS580'}
2025-08-03 15:31:10 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 12 条记录
2025-08-03 15:31:11 | 调用API: concept_detail, 参数: {'id': 'TS581'}
2025-08-03 15:31:11 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:31:11 | 调用API: concept_detail, 参数: {'id': 'TS582'}
2025-08-03 15:31:11 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 58 条记录
2025-08-03 15:31:14 | 调用API: concept_detail, 参数: {'id': 'TS583'}
2025-08-03 15:31:14 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 12 条记录
2025-08-03 15:31:15 | 调用API: concept_detail, 参数: {'id': 'TS584'}
2025-08-03 15:31:15 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 4 条记录
2025-08-03 15:31:15 | 调用API: concept_detail, 参数: {'id': 'TS585'}
2025-08-03 15:31:15 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:31:16 | 调用API: concept_detail, 参数: {'id': 'TS586'}
2025-08-03 15:31:16 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 81 条记录
2025-08-03 15:31:20 | 调用API: concept_detail, 参数: {'id': 'TS587'}
2025-08-03 15:31:21 | API调用成功: concept_detail, 耗时: 0.23秒, 返回 203 条记录
2025-08-03 15:31:28 | 调用API: concept_detail, 参数: {'id': 'TS588'}
2025-08-03 15:31:28 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 12 条记录
2025-08-03 15:31:29 | 调用API: concept_detail, 参数: {'id': 'TS589'}
2025-08-03 15:31:29 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:31:29 | 调用API: concept_detail, 参数: {'id': 'TS590'}
2025-08-03 15:31:29 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 14 条记录
2025-08-03 15:31:30 | 调用API: concept_detail, 参数: {'id': 'TS591'}
2025-08-03 15:31:30 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 25 条记录
2025-08-03 15:31:31 | 调用API: concept_detail, 参数: {'id': 'TS592'}
2025-08-03 15:31:31 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:31:31 | 调用API: concept_detail, 参数: {'id': 'TS593'}
2025-08-03 15:31:31 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 1 条记录
2025-08-03 15:31:31 | 调用API: concept_detail, 参数: {'id': 'TS594'}
2025-08-03 15:31:31 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 89 条记录
2025-08-03 15:31:33 | 调用API: concept_detail, 参数: {'id': 'TS595'}
2025-08-03 15:31:34 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 3 条记录
2025-08-03 15:31:34 | 调用API: concept_detail, 参数: {'id': 'TS596'}
2025-08-03 15:31:34 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 26 条记录
2025-08-03 15:31:35 | 调用API: concept_detail, 参数: {'id': 'TS597'}
2025-08-03 15:31:35 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 5 条记录
2025-08-03 15:31:35 | 调用API: concept_detail, 参数: {'id': 'TS598'}
2025-08-03 15:31:35 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:31:35 | 调用API: concept_detail, 参数: {'id': 'TS599'}
2025-08-03 15:31:36 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 25 条记录
2025-08-03 15:31:36 | 调用API: concept_detail, 参数: {'id': 'TS600'}
2025-08-03 15:31:36 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 9 条记录
2025-08-03 15:31:37 | 调用API: concept_detail, 参数: {'id': 'TS601'}
2025-08-03 15:31:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 5 条记录
2025-08-03 15:31:37 | 调用API: concept_detail, 参数: {'id': 'TS602'}
2025-08-03 15:31:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 47 条记录
2025-08-03 15:31:38 | 调用API: concept_detail, 参数: {'id': 'TS603'}
2025-08-03 15:31:38 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 14 条记录
2025-08-03 15:31:39 | 调用API: concept_detail, 参数: {'id': 'TS604'}
2025-08-03 15:31:39 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 32 条记录
2025-08-03 15:31:40 | 调用API: concept_detail, 参数: {'id': 'TS605'}
2025-08-03 15:31:40 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 87 条记录
2025-08-03 15:31:42 | 调用API: concept_detail, 参数: {'id': 'TS606'}
2025-08-03 15:31:43 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 8 条记录
2025-08-03 15:31:43 | 调用API: concept_detail, 参数: {'id': 'TS607'}
2025-08-03 15:31:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 13 条记录
2025-08-03 15:31:44 | 调用API: concept_detail, 参数: {'id': 'TS608'}
2025-08-03 15:31:44 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:31:44 | 调用API: concept_detail, 参数: {'id': 'TS609'}
2025-08-03 15:31:44 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 720 条记录
2025-08-03 15:32:01 | 调用API: concept_detail, 参数: {'id': 'TS610'}
2025-08-03 15:32:01 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 35 条记录
2025-08-03 15:32:02 | 调用API: concept_detail, 参数: {'id': 'TS611'}
2025-08-03 15:32:02 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 15 条记录
2025-08-03 15:32:02 | 调用API: concept_detail, 参数: {'id': 'TS612'}
2025-08-03 15:32:02 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 95 条记录
2025-08-03 15:32:05 | 调用API: concept_detail, 参数: {'id': 'TS613'}
2025-08-03 15:32:05 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 27 条记录
2025-08-03 15:32:06 | 调用API: concept_detail, 参数: {'id': 'TS614'}
2025-08-03 15:32:06 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 41 条记录
2025-08-03 15:32:07 | 调用API: concept_detail, 参数: {'id': 'TS615'}
2025-08-03 15:32:07 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 7 条记录
2025-08-03 15:32:07 | 调用API: concept_detail, 参数: {'id': 'TS616'}
2025-08-03 15:32:07 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 15 条记录
2025-08-03 15:32:08 | 调用API: concept_detail, 参数: {'id': 'TS617'}
2025-08-03 15:32:08 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 67 条记录
2025-08-03 15:32:10 | 调用API: concept_detail, 参数: {'id': 'TS618'}
2025-08-03 15:32:10 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 55 条记录
2025-08-03 15:32:11 | 调用API: concept_detail, 参数: {'id': 'TS619'}
2025-08-03 15:32:11 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 5 条记录
2025-08-03 15:32:11 | 调用API: concept_detail, 参数: {'id': 'TS620'}
2025-08-03 15:32:11 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 22 条记录
2025-08-03 15:32:12 | 调用API: concept_detail, 参数: {'id': 'TS621'}
2025-08-03 15:32:12 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 33 条记录
2025-08-03 15:32:13 | 调用API: concept_detail, 参数: {'id': 'TS622'}
2025-08-03 15:32:13 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 143 条记录
2025-08-03 15:32:16 | 调用API: concept_detail, 参数: {'id': 'TS623'}
2025-08-03 15:32:17 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 35 条记录
2025-08-03 15:32:18 | 调用API: concept_detail, 参数: {'id': 'TS624'}
2025-08-03 15:32:18 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 52 条记录
2025-08-03 15:32:19 | 调用API: concept_detail, 参数: {'id': 'TS625'}
2025-08-03 15:32:19 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 50 条记录
2025-08-03 15:32:20 | 调用API: concept_detail, 参数: {'id': 'TS626'}
2025-08-03 15:32:21 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 7 条记录
2025-08-03 15:32:21 | 调用API: concept_detail, 参数: {'id': 'TS627'}
2025-08-03 15:32:21 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:32:21 | 调用API: concept_detail, 参数: {'id': 'TS628'}
2025-08-03 15:32:21 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 18 条记录
2025-08-03 15:32:22 | 调用API: concept_detail, 参数: {'id': 'TS629'}
2025-08-03 15:32:22 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 89 条记录
2025-08-03 15:32:24 | 调用API: concept_detail, 参数: {'id': 'TS630'}
2025-08-03 15:32:24 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 31 条记录
2025-08-03 15:32:25 | 调用API: concept_detail, 参数: {'id': 'TS631'}
2025-08-03 15:32:25 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 18 条记录
2025-08-03 15:32:25 | 调用API: concept_detail, 参数: {'id': 'TS632'}
2025-08-03 15:32:25 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:32:26 | 调用API: concept_detail, 参数: {'id': 'TS633'}
2025-08-03 15:32:26 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 31 条记录
2025-08-03 15:32:27 | 调用API: concept_detail, 参数: {'id': 'TS634'}
2025-08-03 15:32:27 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 130 条记录
2025-08-03 15:32:30 | 调用API: concept_detail, 参数: {'id': 'TS635'}
2025-08-03 15:32:30 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 6 条记录
2025-08-03 15:32:30 | 调用API: concept_detail, 参数: {'id': 'TS636'}
2025-08-03 15:32:31 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 19 条记录
2025-08-03 15:32:31 | 调用API: concept_detail, 参数: {'id': 'TS637'}
2025-08-03 15:32:31 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 8 条记录
2025-08-03 15:32:31 | 调用API: concept_detail, 参数: {'id': 'TS638'}
2025-08-03 15:32:32 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 78 条记录
2025-08-03 15:32:33 | 调用API: concept_detail, 参数: {'id': 'TS640'}
2025-08-03 15:32:33 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:32:34 | 调用API: concept_detail, 参数: {'id': 'TS641'}
2025-08-03 15:32:34 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 27 条记录
2025-08-03 15:32:35 | 调用API: concept_detail, 参数: {'id': 'TS642'}
2025-08-03 15:32:35 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 3 条记录
2025-08-03 15:32:35 | 调用API: concept_detail, 参数: {'id': 'TS643'}
2025-08-03 15:32:35 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 46 条记录
2025-08-03 15:32:36 | 调用API: concept_detail, 参数: {'id': 'TS644'}
2025-08-03 15:32:36 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 45 条记录
2025-08-03 15:32:37 | 调用API: concept_detail, 参数: {'id': 'TS645'}
2025-08-03 15:32:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 15 条记录
2025-08-03 15:32:38 | 调用API: concept_detail, 参数: {'id': 'TS646'}
2025-08-03 15:32:38 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 154 条记录
2025-08-03 15:32:42 | 调用API: concept_detail, 参数: {'id': 'TS647'}
2025-08-03 15:32:42 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 19 条记录
2025-08-03 15:32:42 | 调用API: concept_detail, 参数: {'id': 'TS648'}
2025-08-03 15:32:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 5 条记录
2025-08-03 15:32:43 | 调用API: concept_detail, 参数: {'id': 'TS649'}
2025-08-03 15:32:43 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 20 条记录
2025-08-03 15:32:43 | 调用API: concept_detail, 参数: {'id': 'TS650'}
2025-08-03 15:32:44 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 127 条记录
2025-08-03 15:32:47 | 调用API: concept_detail, 参数: {'id': 'TS651'}
2025-08-03 15:32:47 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 80 条记录
2025-08-03 15:32:48 | 调用API: concept_detail, 参数: {'id': 'TS652'}
2025-08-03 15:32:49 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 11 条记录
2025-08-03 15:32:49 | 调用API: concept_detail, 参数: {'id': 'TS653'}
2025-08-03 15:32:49 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 153 条记录
2025-08-03 15:32:53 | 调用API: concept_detail, 参数: {'id': 'TS654'}
2025-08-03 15:32:53 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 1 条记录
2025-08-03 15:32:53 | 调用API: concept_detail, 参数: {'id': 'TS655'}
2025-08-03 15:32:53 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 120 条记录
2025-08-03 15:32:56 | 调用API: concept_detail, 参数: {'id': 'TS656'}
2025-08-03 15:32:56 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 87 条记录
2025-08-03 15:32:58 | 调用API: concept_detail, 参数: {'id': 'TS657'}
2025-08-03 15:32:58 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 15 条记录
2025-08-03 15:32:59 | 调用API: concept_detail, 参数: {'id': 'TS658'}
2025-08-03 15:32:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 11 条记录
2025-08-03 15:32:59 | 调用API: concept_detail, 参数: {'id': 'TS659'}
2025-08-03 15:32:59 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 4 条记录
2025-08-03 15:32:59 | 调用API: concept_detail, 参数: {'id': 'TS660'}
2025-08-03 15:33:00 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 12 条记录
2025-08-03 15:33:00 | 调用API: concept_detail, 参数: {'id': 'TS661'}
2025-08-03 15:33:00 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 17 条记录
2025-08-03 15:33:01 | 调用API: concept_detail, 参数: {'id': 'TS662'}
2025-08-03 15:33:01 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 76 条记录
2025-08-03 15:33:03 | 调用API: concept_detail, 参数: {'id': 'TS663'}
2025-08-03 15:33:03 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 5 条记录
2025-08-03 15:33:04 | 调用API: concept_detail, 参数: {'id': 'TS664'}
2025-08-03 15:33:04 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 7 条记录
2025-08-03 15:33:04 | 调用API: concept_detail, 参数: {'id': 'TS665'}
2025-08-03 15:33:04 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 20 条记录
2025-08-03 15:33:05 | 调用API: concept_detail, 参数: {'id': 'TS666'}
2025-08-03 15:33:05 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 26 条记录
2025-08-03 15:33:06 | 调用API: concept_detail, 参数: {'id': 'TS667'}
2025-08-03 15:33:06 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 17 条记录
2025-08-03 15:33:06 | 调用API: concept_detail, 参数: {'id': 'TS668'}
2025-08-03 15:33:06 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 410 条记录
2025-08-03 15:33:15 | 调用API: concept_detail, 参数: {'id': 'TS669'}
2025-08-03 15:33:16 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 29 条记录
2025-08-03 15:33:16 | 调用API: concept_detail, 参数: {'id': 'TS670'}
2025-08-03 15:33:16 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:33:17 | 调用API: concept_detail, 参数: {'id': 'TS671'}
2025-08-03 15:33:17 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 24 条记录
2025-08-03 15:33:17 | 调用API: concept_detail, 参数: {'id': 'TS672'}
2025-08-03 15:33:18 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:33:18 | 调用API: concept_detail, 参数: {'id': 'TS673'}
2025-08-03 15:33:18 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 25 条记录
2025-08-03 15:33:18 | 调用API: concept_detail, 参数: {'id': 'TS674'}
2025-08-03 15:33:19 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 181 条记录
2025-08-03 15:33:22 | 调用API: concept_detail, 参数: {'id': 'TS675'}
2025-08-03 15:33:23 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 6 条记录
2025-08-03 15:33:23 | 调用API: concept_detail, 参数: {'id': 'TS676'}
2025-08-03 15:33:23 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 117 条记录
2025-08-03 15:33:26 | 调用API: concept_detail, 参数: {'id': 'TS677'}
2025-08-03 15:33:26 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 11 条记录
2025-08-03 15:33:26 | 调用API: concept_detail, 参数: {'id': 'TS678'}
2025-08-03 15:33:26 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 49 条记录
2025-08-03 15:33:28 | 调用API: concept_detail, 参数: {'id': 'TS679'}
2025-08-03 15:33:28 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 26 条记录
2025-08-03 15:33:29 | 调用API: concept_detail, 参数: {'id': 'TS680'}
2025-08-03 15:33:29 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:33:29 | 调用API: concept_detail, 参数: {'id': 'TS681'}
2025-08-03 15:33:29 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 24 条记录
2025-08-03 15:33:30 | 调用API: concept_detail, 参数: {'id': 'TS682'}
2025-08-03 15:33:30 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 40 条记录
2025-08-03 15:33:31 | 调用API: concept_detail, 参数: {'id': 'TS683'}
2025-08-03 15:33:31 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 20 条记录
2025-08-03 15:33:32 | 调用API: concept_detail, 参数: {'id': 'TS684'}
2025-08-03 15:33:32 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 20 条记录
2025-08-03 15:33:33 | 调用API: concept_detail, 参数: {'id': 'TS685'}
2025-08-03 15:33:33 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 16 条记录
2025-08-03 15:33:33 | 调用API: concept_detail, 参数: {'id': 'TS686'}
2025-08-03 15:33:33 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 34 条记录
2025-08-03 15:33:34 | 调用API: concept_detail, 参数: {'id': 'TS687'}
2025-08-03 15:33:34 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 13 条记录
2025-08-03 15:33:35 | 调用API: concept_detail, 参数: {'id': 'TS688'}
2025-08-03 15:33:35 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 8 条记录
2025-08-03 15:33:35 | 调用API: concept_detail, 参数: {'id': 'TS689'}
2025-08-03 15:33:35 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 87 条记录
2025-08-03 15:33:37 | 调用API: concept_detail, 参数: {'id': 'TS690'}
2025-08-03 15:33:37 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 270 条记录
2025-08-03 15:33:43 | 调用API: concept_detail, 参数: {'id': 'TS691'}
2025-08-03 15:33:43 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 180 条记录
2025-08-03 15:33:48 | 调用API: concept_detail, 参数: {'id': 'TS692'}
2025-08-03 15:33:48 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 59 条记录
2025-08-03 15:33:51 | 调用API: concept_detail, 参数: {'id': 'TS693'}
2025-08-03 15:33:51 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 9 条记录
2025-08-03 15:33:51 | 调用API: concept_detail, 参数: {'id': 'TS694'}
2025-08-03 15:33:52 | API调用成功: concept_detail, 耗时: 0.19秒, 返回 68 条记录
2025-08-03 15:33:53 | 调用API: concept_detail, 参数: {'id': 'TS695'}
2025-08-03 15:33:54 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 16 条记录
2025-08-03 15:33:54 | 调用API: concept_detail, 参数: {'id': 'TS696'}
2025-08-03 15:33:54 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:33:55 | 调用API: concept_detail, 参数: {'id': 'TS697'}
2025-08-03 15:33:55 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 29 条记录
2025-08-03 15:33:56 | 调用API: concept_detail, 参数: {'id': 'TS698'}
2025-08-03 15:33:56 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:33:56 | 调用API: concept_detail, 参数: {'id': 'TS699'}
2025-08-03 15:33:56 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 37 条记录
2025-08-03 15:33:57 | 调用API: concept_detail, 参数: {'id': 'TS700'}
2025-08-03 15:33:57 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 8 条记录
2025-08-03 15:33:58 | 调用API: concept_detail, 参数: {'id': 'TS701'}
2025-08-03 15:33:58 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 23 条记录
2025-08-03 15:33:58 | 调用API: concept_detail, 参数: {'id': 'TS702'}
2025-08-03 15:33:58 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:33:59 | 调用API: concept_detail, 参数: {'id': 'TS703'}
2025-08-03 15:33:59 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:33:59 | 调用API: concept_detail, 参数: {'id': 'TS704'}
2025-08-03 15:33:59 | API调用成功: concept_detail, 耗时: 0.08秒, 返回 32 条记录
2025-08-03 15:34:00 | 调用API: concept_detail, 参数: {'id': 'TS705'}
2025-08-03 15:34:00 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 6 条记录
2025-08-03 15:34:00 | 调用API: concept_detail, 参数: {'id': 'TS706'}
2025-08-03 15:34:00 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:34:01 | 调用API: concept_detail, 参数: {'id': 'TS707'}
2025-08-03 15:34:01 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 7 条记录
2025-08-03 15:34:01 | 调用API: concept_detail, 参数: {'id': 'TS708'}
2025-08-03 15:34:01 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 132 条记录
2025-08-03 15:34:04 | 调用API: concept_detail, 参数: {'id': 'TS709'}
2025-08-03 15:34:04 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 402 条记录
2025-08-03 15:34:12 | 调用API: concept_detail, 参数: {'id': 'TS710'}
2025-08-03 15:34:12 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 21 条记录
2025-08-03 15:34:13 | 调用API: concept_detail, 参数: {'id': 'TS711'}
2025-08-03 15:34:13 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:34:13 | 调用API: concept_detail, 参数: {'id': 'TS712'}
2025-08-03 15:34:13 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 14 条记录
2025-08-03 15:34:14 | 调用API: concept_detail, 参数: {'id': 'TS713'}
2025-08-03 15:34:14 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 13 条记录
2025-08-03 15:34:14 | 调用API: concept_detail, 参数: {'id': 'TS714'}
2025-08-03 15:34:14 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 104 条记录
2025-08-03 15:34:17 | 调用API: concept_detail, 参数: {'id': 'TS715'}
2025-08-03 15:34:17 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 53 条记录
2025-08-03 15:34:18 | 调用API: concept_detail, 参数: {'id': 'TS716'}
2025-08-03 15:34:18 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 15 条记录
2025-08-03 15:34:18 | 调用API: concept_detail, 参数: {'id': 'TS717'}
2025-08-03 15:34:19 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 11 条记录
2025-08-03 15:34:19 | 调用API: concept_detail, 参数: {'id': 'TS718'}
2025-08-03 15:34:19 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 11 条记录
2025-08-03 15:34:19 | 调用API: concept_detail, 参数: {'id': 'TS719'}
2025-08-03 15:34:20 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 3 条记录
2025-08-03 15:34:20 | 调用API: concept_detail, 参数: {'id': 'TS720'}
2025-08-03 15:34:20 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 4 条记录
2025-08-03 15:34:20 | 调用API: concept_detail, 参数: {'id': 'TS721'}
2025-08-03 15:34:20 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 5 条记录
2025-08-03 15:34:21 | 调用API: concept_detail, 参数: {'id': 'TS722'}
2025-08-03 15:34:21 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 10 条记录
2025-08-03 15:34:21 | 调用API: concept_detail, 参数: {'id': 'TS723'}
2025-08-03 15:34:21 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 9 条记录
2025-08-03 15:34:21 | 调用API: concept_detail, 参数: {'id': 'TS724'}
2025-08-03 15:34:21 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 4 条记录
2025-08-03 15:34:22 | 调用API: concept_detail, 参数: {'id': 'TS725'}
2025-08-03 15:34:22 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 16 条记录
2025-08-03 15:34:22 | 调用API: concept_detail, 参数: {'id': 'TS726'}
2025-08-03 15:34:22 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 10 条记录
2025-08-03 15:34:23 | 调用API: concept_detail, 参数: {'id': 'TS727'}
2025-08-03 15:34:23 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 37 条记录
2025-08-03 15:34:24 | 调用API: concept_detail, 参数: {'id': 'TS728'}
2025-08-03 15:34:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 34 条记录
2025-08-03 15:34:25 | 调用API: concept_detail, 参数: {'id': 'TS729'}
2025-08-03 15:34:25 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 33 条记录
2025-08-03 15:34:26 | 调用API: concept_detail, 参数: {'id': 'TS730'}
2025-08-03 15:34:26 | API调用成功: concept_detail, 耗时: 0.21秒, 返回 36 条记录
2025-08-03 15:34:27 | 调用API: concept_detail, 参数: {'id': 'TS731'}
2025-08-03 15:34:27 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 14 条记录
2025-08-03 15:34:27 | 调用API: concept_detail, 参数: {'id': 'TS732'}
2025-08-03 15:34:28 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 223 条记录
2025-08-03 15:34:32 | 调用API: concept_detail, 参数: {'id': 'TS733'}
2025-08-03 15:34:32 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 121 条记录
2025-08-03 15:34:34 | 调用API: concept_detail, 参数: {'id': 'TS734'}
2025-08-03 15:34:35 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 53 条记录
2025-08-03 15:34:36 | 调用API: concept_detail, 参数: {'id': 'TS735'}
2025-08-03 15:34:36 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 14 条记录
2025-08-03 15:34:36 | 调用API: concept_detail, 参数: {'id': 'TS736'}
2025-08-03 15:34:36 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 15 条记录
2025-08-03 15:34:37 | 调用API: concept_detail, 参数: {'id': 'TS737'}
2025-08-03 15:34:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 43 条记录
2025-08-03 15:34:38 | 调用API: concept_detail, 参数: {'id': 'TS738'}
2025-08-03 15:34:38 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 90 条记录
2025-08-03 15:34:40 | 调用API: concept_detail, 参数: {'id': 'TS739'}
2025-08-03 15:34:40 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 48 条记录
2025-08-03 15:34:41 | 调用API: concept_detail, 参数: {'id': 'TS740'}
2025-08-03 15:34:41 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 26 条记录
2025-08-03 15:34:42 | 调用API: concept_detail, 参数: {'id': 'TS741'}
2025-08-03 15:34:42 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 33 条记录
2025-08-03 15:34:42 | 调用API: concept_detail, 参数: {'id': 'TS742'}
2025-08-03 15:34:43 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 7 条记录
2025-08-03 15:34:43 | 调用API: concept_detail, 参数: {'id': 'TS743'}
2025-08-03 15:34:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 12 条记录
2025-08-03 15:34:43 | 调用API: concept_detail, 参数: {'id': 'TS744'}
2025-08-03 15:34:43 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:34:44 | 调用API: concept_detail, 参数: {'id': 'TS745'}
2025-08-03 15:34:44 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 46 条记录
2025-08-03 15:34:45 | 调用API: concept_detail, 参数: {'id': 'TS746'}
2025-08-03 15:34:45 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 2 条记录
2025-08-03 15:34:45 | 调用API: concept_detail, 参数: {'id': 'TS747'}
2025-08-03 15:34:45 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:34:46 | 调用API: concept_detail, 参数: {'id': 'TS748'}
2025-08-03 15:34:46 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 4 条记录
2025-08-03 15:34:46 | 调用API: concept_detail, 参数: {'id': 'TS749'}
2025-08-03 15:34:46 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 58 条记录
2025-08-03 15:34:47 | 调用API: concept_detail, 参数: {'id': 'TS750'}
2025-08-03 15:34:48 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 17 条记录
2025-08-03 15:34:48 | 调用API: concept_detail, 参数: {'id': 'TS751'}
2025-08-03 15:34:48 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 43 条记录
2025-08-03 15:34:49 | 调用API: concept_detail, 参数: {'id': 'TS752'}
2025-08-03 15:34:49 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 33 条记录
2025-08-03 15:34:50 | 调用API: concept_detail, 参数: {'id': 'TS753'}
2025-08-03 15:34:50 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 13 条记录
2025-08-03 15:34:50 | 调用API: concept_detail, 参数: {'id': 'TS754'}
2025-08-03 15:34:51 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 52 条记录
2025-08-03 15:34:52 | 调用API: concept_detail, 参数: {'id': 'TS755'}
2025-08-03 15:34:52 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 38 条记录
2025-08-03 15:34:53 | 调用API: concept_detail, 参数: {'id': 'TS756'}
2025-08-03 15:34:53 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 70 条记录
2025-08-03 15:34:54 | 调用API: concept_detail, 参数: {'id': 'TS757'}
2025-08-03 15:34:54 | API调用成功: concept_detail, 耗时: 0.08秒, 返回 6 条记录
2025-08-03 15:34:55 | 调用API: concept_detail, 参数: {'id': 'TS758'}
2025-08-03 15:34:55 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 13 条记录
2025-08-03 15:34:55 | 调用API: concept_detail, 参数: {'id': 'TS759'}
2025-08-03 15:34:55 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 59 条记录
2025-08-03 15:34:56 | 调用API: concept_detail, 参数: {'id': 'TS760'}
2025-08-03 15:34:57 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 15 条记录
2025-08-03 15:34:57 | 调用API: concept_detail, 参数: {'id': 'TS761'}
2025-08-03 15:34:57 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 6 条记录
2025-08-03 15:34:57 | 调用API: concept_detail, 参数: {'id': 'TS762'}
2025-08-03 15:34:57 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 41 条记录
2025-08-03 15:34:58 | 调用API: concept_detail, 参数: {'id': 'TS763'}
2025-08-03 15:34:58 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 11 条记录
2025-08-03 15:34:59 | 调用API: concept_detail, 参数: {'id': 'TS764'}
2025-08-03 15:34:59 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 4 条记录
2025-08-03 15:34:59 | 调用API: concept_detail, 参数: {'id': 'TS765'}
2025-08-03 15:34:59 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 128 条记录
2025-08-03 15:35:02 | 调用API: concept_detail, 参数: {'id': 'TS766'}
2025-08-03 15:35:02 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 28 条记录
2025-08-03 15:35:03 | 调用API: concept_detail, 参数: {'id': 'TS767'}
2025-08-03 15:35:03 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 8 条记录
2025-08-03 15:35:03 | 调用API: concept_detail, 参数: {'id': 'TS768'}
2025-08-03 15:35:03 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:35:03 | 调用API: concept_detail, 参数: {'id': 'TS769'}
2025-08-03 15:35:03 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 6 条记录
2025-08-03 15:35:04 | 调用API: concept_detail, 参数: {'id': 'TS770'}
2025-08-03 15:35:04 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 55 条记录
2025-08-03 15:35:05 | 调用API: concept_detail, 参数: {'id': 'TS771'}
2025-08-03 15:35:05 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 10 条记录
2025-08-03 15:35:06 | 调用API: concept_detail, 参数: {'id': 'TS772'}
2025-08-03 15:35:06 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 25 条记录
2025-08-03 15:35:06 | 调用API: concept_detail, 参数: {'id': 'TS773'}
2025-08-03 15:35:07 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 6 条记录
2025-08-03 15:35:07 | 调用API: concept_detail, 参数: {'id': 'TS774'}
2025-08-03 15:35:07 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 12 条记录
2025-08-03 15:35:07 | 调用API: concept_detail, 参数: {'id': 'TS775'}
2025-08-03 15:35:07 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 9 条记录
2025-08-03 15:35:08 | 调用API: concept_detail, 参数: {'id': 'TS776'}
2025-08-03 15:35:08 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 139 条记录
2025-08-03 15:35:11 | 调用API: concept_detail, 参数: {'id': 'TS777'}
2025-08-03 15:35:11 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:35:11 | 调用API: concept_detail, 参数: {'id': 'TS778'}
2025-08-03 15:35:12 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 5 条记录
2025-08-03 15:35:12 | 调用API: concept_detail, 参数: {'id': 'TS779'}
2025-08-03 15:35:12 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 16 条记录
2025-08-03 15:35:13 | 调用API: concept_detail, 参数: {'id': 'TS780'}
2025-08-03 15:35:13 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 82 条记录
2025-08-03 15:35:15 | 调用API: concept_detail, 参数: {'id': 'TS781'}
2025-08-03 15:35:15 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:35:16 | 调用API: concept_detail, 参数: {'id': 'TS782'}
2025-08-03 15:35:16 | API调用成功: concept_detail, 耗时: 0.25秒, 返回 24 条记录
2025-08-03 15:35:17 | 调用API: concept_detail, 参数: {'id': 'TS783'}
2025-08-03 15:35:18 | 调用API: concept_detail, 参数: {'id': 'TS784'}
2025-08-03 15:35:18 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 6 条记录
2025-08-03 15:35:18 | 调用API: concept_detail, 参数: {'id': 'TS785'}
2025-08-03 15:35:18 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 167 条记录
2025-08-03 15:35:22 | 调用API: concept_detail, 参数: {'id': 'TS786'}
2025-08-03 15:35:23 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 20 条记录
2025-08-03 15:35:23 | 调用API: concept_detail, 参数: {'id': 'TS787'}
2025-08-03 15:35:23 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 6 条记录
2025-08-03 15:35:24 | 调用API: concept_detail, 参数: {'id': 'TS788'}
2025-08-03 15:35:24 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 12 条记录
2025-08-03 15:35:24 | 调用API: concept_detail, 参数: {'id': 'TS789'}
2025-08-03 15:35:24 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 29 条记录
2025-08-03 15:35:25 | 调用API: concept_detail, 参数: {'id': 'TS790'}
2025-08-03 15:35:25 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 68 条记录
2025-08-03 15:35:27 | 调用API: concept_detail, 参数: {'id': 'TS791'}
2025-08-03 15:35:27 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 10 条记录
2025-08-03 15:35:28 | 调用API: concept_detail, 参数: {'id': 'TS792'}
2025-08-03 15:35:28 | 调用API: concept_detail, 参数: {'id': 'TS793'}
2025-08-03 15:35:28 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 13 条记录
2025-08-03 15:35:28 | 调用API: concept_detail, 参数: {'id': 'TS794'}
2025-08-03 15:35:28 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 6 条记录
2025-08-03 15:35:29 | 调用API: concept_detail, 参数: {'id': 'TS795'}
2025-08-03 15:35:29 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 29 条记录
2025-08-03 15:35:30 | 调用API: concept_detail, 参数: {'id': 'TS796'}
2025-08-03 15:35:30 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 15 条记录
2025-08-03 15:35:31 | 调用API: concept_detail, 参数: {'id': 'TS797'}
2025-08-03 15:35:31 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 1 条记录
2025-08-03 15:35:31 | 调用API: concept_detail, 参数: {'id': 'TS798'}
2025-08-03 15:35:31 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 10 条记录
2025-08-03 15:35:31 | 调用API: concept_detail, 参数: {'id': 'TS799'}
2025-08-03 15:35:31 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 29 条记录
2025-08-03 15:35:32 | 调用API: concept_detail, 参数: {'id': 'TS800'}
2025-08-03 15:35:32 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 15 条记录
2025-08-03 15:35:33 | 调用API: concept_detail, 参数: {'id': 'TS801'}
2025-08-03 15:35:33 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 26 条记录
2025-08-03 15:35:34 | 调用API: concept_detail, 参数: {'id': 'TS802'}
2025-08-03 15:35:34 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 10 条记录
2025-08-03 15:35:34 | 调用API: concept_detail, 参数: {'id': 'TS803'}
2025-08-03 15:35:34 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 6 条记录
2025-08-03 15:35:35 | 调用API: concept_detail, 参数: {'id': 'TS804'}
2025-08-03 15:35:35 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 46 条记录
2025-08-03 15:35:36 | 调用API: concept_detail, 参数: {'id': 'TS805'}
2025-08-03 15:35:36 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 9 条记录
2025-08-03 15:35:37 | 调用API: concept_detail, 参数: {'id': 'TS806'}
2025-08-03 15:35:37 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 21 条记录
2025-08-03 15:35:37 | 调用API: concept_detail, 参数: {'id': 'TS807'}
2025-08-03 15:35:37 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 5 条记录
2025-08-03 15:35:38 | 调用API: concept_detail, 参数: {'id': 'TS808'}
2025-08-03 15:35:38 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 14 条记录
2025-08-03 15:35:38 | 调用API: concept_detail, 参数: {'id': 'TS809'}
2025-08-03 15:35:38 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 4 条记录
2025-08-03 15:35:39 | 调用API: concept_detail, 参数: {'id': 'TS810'}
2025-08-03 15:35:39 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 12 条记录
2025-08-03 15:35:39 | 调用API: concept_detail, 参数: {'id': 'TS811'}
2025-08-03 15:35:39 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 12 条记录
2025-08-03 15:35:40 | 调用API: concept_detail, 参数: {'id': 'TS812'}
2025-08-03 15:35:40 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 2 条记录
2025-08-03 15:35:40 | 调用API: concept_detail, 参数: {'id': 'TS813'}
2025-08-03 15:35:40 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 15 条记录
2025-08-03 15:35:40 | 调用API: concept_detail, 参数: {'id': 'TS814'}
2025-08-03 15:35:41 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 46 条记录
2025-08-03 15:35:42 | 调用API: concept_detail, 参数: {'id': 'TS815'}
2025-08-03 15:35:42 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 8 条记录
2025-08-03 15:35:42 | 调用API: concept_detail, 参数: {'id': 'TS816'}
2025-08-03 15:35:42 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 9 条记录
2025-08-03 15:35:43 | 调用API: concept_detail, 参数: {'id': 'TS817'}
2025-08-03 15:35:43 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 97 条记录
2025-08-03 15:35:45 | 调用API: concept_detail, 参数: {'id': 'TS818'}
2025-08-03 15:35:45 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 17 条记录
2025-08-03 15:35:46 | 调用API: concept_detail, 参数: {'id': 'TS819'}
2025-08-03 15:35:46 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 5 条记录
2025-08-03 15:35:46 | 调用API: concept_detail, 参数: {'id': 'TS820'}
2025-08-03 15:35:46 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 4 条记录
2025-08-03 15:35:46 | 调用API: concept_detail, 参数: {'id': 'TS821'}
2025-08-03 15:35:47 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 10 条记录
2025-08-03 15:35:47 | 调用API: concept_detail, 参数: {'id': 'TS822'}
2025-08-03 15:35:47 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 21 条记录
2025-08-03 15:35:48 | 调用API: concept_detail, 参数: {'id': 'TS823'}
2025-08-03 15:35:48 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 40 条记录
2025-08-03 15:35:49 | 调用API: concept_detail, 参数: {'id': 'TS824'}
2025-08-03 15:35:49 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:35:49 | 调用API: concept_detail, 参数: {'id': 'TS825'}
2025-08-03 15:35:49 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:35:49 | 调用API: concept_detail, 参数: {'id': 'TS826'}
2025-08-03 15:35:49 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 28 条记录
2025-08-03 15:35:50 | 调用API: concept_detail, 参数: {'id': 'TS827'}
2025-08-03 15:35:50 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 7 条记录
2025-08-03 15:35:50 | 调用API: concept_detail, 参数: {'id': 'TS828'}
2025-08-03 15:35:51 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 52 条记录
2025-08-03 15:35:52 | 调用API: concept_detail, 参数: {'id': 'TS829'}
2025-08-03 15:35:52 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 67 条记录
2025-08-03 15:35:53 | 调用API: concept_detail, 参数: {'id': 'TS830'}
2025-08-03 15:35:53 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 24 条记录
2025-08-03 15:35:54 | 调用API: concept_detail, 参数: {'id': 'TS831'}
2025-08-03 15:35:54 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 3 条记录
2025-08-03 15:35:54 | 调用API: concept_detail, 参数: {'id': 'TS832'}
2025-08-03 15:35:54 | API调用成功: concept_detail, 耗时: 0.20秒, 返回 30 条记录
2025-08-03 15:35:55 | 调用API: concept_detail, 参数: {'id': 'TS833'}
2025-08-03 15:35:55 | API调用成功: concept_detail, 耗时: 0.15秒, 返回 7 条记录
2025-08-03 15:35:55 | 调用API: concept_detail, 参数: {'id': 'TS834'}
2025-08-03 15:35:56 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 8 条记录
2025-08-03 15:35:56 | 调用API: concept_detail, 参数: {'id': 'TS835'}
2025-08-03 15:35:56 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 26 条记录
2025-08-03 15:35:57 | 调用API: concept_detail, 参数: {'id': 'TS836'}
2025-08-03 15:35:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 7 条记录
2025-08-03 15:35:57 | 调用API: concept_detail, 参数: {'id': 'TS837'}
2025-08-03 15:35:57 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 31 条记录
2025-08-03 15:35:58 | 调用API: concept_detail, 参数: {'id': 'TS838'}
2025-08-03 15:35:58 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 4 条记录
2025-08-03 15:35:58 | 调用API: concept_detail, 参数: {'id': 'TS839'}
2025-08-03 15:35:58 | API调用成功: concept_detail, 耗时: 0.18秒, 返回 214 条记录
2025-08-03 15:36:03 | 调用API: concept_detail, 参数: {'id': 'TS840'}
2025-08-03 15:36:03 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 5 条记录
2025-08-03 15:36:03 | 调用API: concept_detail, 参数: {'id': 'TS841'}
2025-08-03 15:36:03 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 19 条记录
2025-08-03 15:36:04 | 调用API: concept_detail, 参数: {'id': 'TS842'}
2025-08-03 15:36:04 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 18 条记录
2025-08-03 15:36:04 | 调用API: concept_detail, 参数: {'id': 'TS843'}
2025-08-03 15:36:04 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 28 条记录
2025-08-03 15:36:05 | 调用API: concept_detail, 参数: {'id': 'TS844'}
2025-08-03 15:36:05 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 22 条记录
2025-08-03 15:36:06 | 调用API: concept_detail, 参数: {'id': 'TS845'}
2025-08-03 15:36:06 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 9 条记录
2025-08-03 15:36:06 | 调用API: concept_detail, 参数: {'id': 'TS846'}
2025-08-03 15:36:06 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 164 条记录
2025-08-03 15:36:10 | 调用API: concept_detail, 参数: {'id': 'TS847'}
2025-08-03 15:36:10 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 31 条记录
2025-08-03 15:36:11 | 调用API: concept_detail, 参数: {'id': 'TS848'}
2025-08-03 15:36:11 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 1 条记录
2025-08-03 15:36:11 | 调用API: concept_detail, 参数: {'id': 'TS849'}
2025-08-03 15:36:11 | 调用API: concept_detail, 参数: {'id': 'TS850'}
2025-08-03 15:36:11 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 8 条记录
2025-08-03 15:36:12 | 调用API: concept_detail, 参数: {'id': 'TS851'}
2025-08-03 15:36:12 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 9 条记录
2025-08-03 15:36:12 | 调用API: concept_detail, 参数: {'id': 'TS852'}
2025-08-03 15:36:12 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 2 条记录
2025-08-03 15:36:12 | 调用API: concept_detail, 参数: {'id': 'TS853'}
2025-08-03 15:36:13 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 31 条记录
2025-08-03 15:36:13 | 调用API: concept_detail, 参数: {'id': 'TS854'}
2025-08-03 15:36:13 | API调用成功: concept_detail, 耗时: 0.08秒, 返回 7 条记录
2025-08-03 15:36:14 | 调用API: concept_detail, 参数: {'id': 'TS855'}
2025-08-03 15:36:14 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 16 条记录
2025-08-03 15:36:14 | 调用API: concept_detail, 参数: {'id': 'TS856'}
2025-08-03 15:36:14 | API调用成功: concept_detail, 耗时: 0.17秒, 返回 13 条记录
2025-08-03 15:36:15 | 调用API: concept_detail, 参数: {'id': 'TS857'}
2025-08-03 15:36:15 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 41 条记录
2025-08-03 15:36:16 | 调用API: concept_detail, 参数: {'id': 'TS858'}
2025-08-03 15:36:16 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 2 条记录
2025-08-03 15:36:16 | 调用API: concept_detail, 参数: {'id': 'TS859'}
2025-08-03 15:36:17 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 19 条记录
2025-08-03 15:36:17 | 调用API: concept_detail, 参数: {'id': 'TS860'}
2025-08-03 15:36:17 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 545 条记录
2025-08-03 15:36:31 | 调用API: concept_detail, 参数: {'id': 'TS861'}
2025-08-03 15:36:31 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 2 条记录
2025-08-03 15:36:32 | 调用API: concept_detail, 参数: {'id': 'TS862'}
2025-08-03 15:36:32 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 6 条记录
2025-08-03 15:36:32 | 调用API: concept_detail, 参数: {'id': 'TS863'}
2025-08-03 15:36:32 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 21 条记录
2025-08-03 15:36:33 | 调用API: concept_detail, 参数: {'id': 'TS864'}
2025-08-03 15:36:33 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 7 条记录
2025-08-03 15:36:33 | 调用API: concept_detail, 参数: {'id': 'TS865'}
2025-08-03 15:36:33 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 32 条记录
2025-08-03 15:36:34 | 调用API: concept_detail, 参数: {'id': 'TS866'}
2025-08-03 15:36:34 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 20 条记录
2025-08-03 15:36:34 | 调用API: concept_detail, 参数: {'id': 'TS867'}
2025-08-03 15:36:35 | API调用成功: concept_detail, 耗时: 0.16秒, 返回 24 条记录
2025-08-03 15:36:35 | 调用API: concept_detail, 参数: {'id': 'TS868'}
2025-08-03 15:36:35 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 7 条记录
2025-08-03 15:36:35 | 调用API: concept_detail, 参数: {'id': 'TS869'}
2025-08-03 15:36:36 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 20 条记录
2025-08-03 15:36:36 | 调用API: concept_detail, 参数: {'id': 'TS870'}
2025-08-03 15:36:36 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 6 条记录
2025-08-03 15:36:36 | 调用API: concept_detail, 参数: {'id': 'TS871'}
2025-08-03 15:36:37 | API调用成功: concept_detail, 耗时: 0.13秒, 返回 13 条记录
2025-08-03 15:36:37 | 调用API: concept_detail, 参数: {'id': 'TS872'}
2025-08-03 15:36:37 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 37 条记录
2025-08-03 15:36:38 | 调用API: concept_detail, 参数: {'id': 'TS873'}
2025-08-03 15:36:38 | API调用成功: concept_detail, 耗时: 0.09秒, 返回 11 条记录
2025-08-03 15:36:38 | 调用API: concept_detail, 参数: {'id': 'TS874'}
2025-08-03 15:36:38 | API调用成功: concept_detail, 耗时: 0.10秒, 返回 34 条记录
2025-08-03 15:36:39 | 调用API: concept_detail, 参数: {'id': 'TS875'}
2025-08-03 15:36:39 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 4 条记录
2025-08-03 15:36:40 | 调用API: concept_detail, 参数: {'id': 'TS876'}
2025-08-03 15:36:40 | API调用成功: concept_detail, 耗时: 0.14秒, 返回 14 条记录
2025-08-03 15:36:40 | 调用API: concept_detail, 参数: {'id': 'TS877'}
2025-08-03 15:36:40 | API调用成功: concept_detail, 耗时: 0.11秒, 返回 8 条记录
2025-08-03 15:36:41 | 调用API: concept_detail, 参数: {'id': 'TS878'}
2025-08-03 15:36:41 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 20 条记录
2025-08-03 15:36:41 | 调用API: concept_detail, 参数: {'id': 'TS879'}
2025-08-03 15:36:41 | API调用成功: concept_detail, 耗时: 0.12秒, 返回 52 条记录
2025-08-03 15:36:43 | 调用API: daily, 参数: {'ts_code': '000001.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:36:43 | API调用成功: daily, 耗时: 0.37秒, 返回 6000 条记录
2025-08-03 15:39:34 | 调用API: daily, 参数: {'ts_code': '000002.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:39:35 | API调用成功: daily, 耗时: 0.42秒, 返回 5986 条记录
2025-08-03 15:41:59 | 调用API: daily, 参数: {'ts_code': '000004.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:42:00 | API调用成功: daily, 耗时: 0.36秒, 返回 5853 条记录
2025-08-03 15:44:45 | 调用API: daily, 参数: {'ts_code': '000006.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:44:46 | API调用成功: daily, 耗时: 0.91秒, 返回 5998 条记录
2025-08-03 15:47:05 | 调用API: daily, 参数: {'ts_code': '000007.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:47:06 | API调用成功: daily, 耗时: 0.30秒, 返回 5486 条记录
2025-08-03 15:49:14 | 调用API: daily, 参数: {'ts_code': '000008.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:49:14 | API调用成功: daily, 耗时: 0.40秒, 返回 5816 条记录
2025-08-03 15:51:34 | 调用API: daily, 参数: {'ts_code': '000009.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:51:34 | API调用成功: daily, 耗时: 0.36秒, 返回 5990 条记录
2025-08-03 15:53:55 | 调用API: daily, 参数: {'ts_code': '000010.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:53:55 | API调用成功: daily, 耗时: 0.34秒, 返回 5202 条记录
2025-08-03 15:55:56 | 调用API: daily, 参数: {'ts_code': '000011.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:55:57 | API调用成功: daily, 耗时: 0.35秒, 返回 5986 条记录
2025-08-03 15:58:14 | 调用API: daily, 参数: {'ts_code': '000012.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 15:58:14 | API调用成功: daily, 耗时: 0.42秒, 返回 6000 条记录
2025-08-03 16:01:20 | 调用API: daily, 参数: {'ts_code': '000014.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:01:20 | API调用成功: daily, 耗时: 0.35秒, 返回 6000 条记录
2025-08-03 16:04:06 | 调用API: daily, 参数: {'ts_code': '000016.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:04:06 | API调用成功: daily, 耗时: 0.34秒, 返回 6000 条记录
2025-08-03 16:06:47 | 调用API: daily, 参数: {'ts_code': '000017.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:06:49 | 调用API: daily, 参数: {'ts_code': '000017.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:06:53 | 调用API: daily, 参数: {'ts_code': '000017.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:06:54 | 调用API: daily, 参数: {'ts_code': '000019.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:06:56 | 调用API: daily, 参数: {'ts_code': '000019.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:07:00 | 调用API: daily, 参数: {'ts_code': '000019.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:07:00 | 调用API: daily, 参数: {'ts_code': '000020.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:07:02 | 调用API: daily, 参数: {'ts_code': '000020.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:07:06 | 调用API: daily, 参数: {'ts_code': '000020.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:07:07 | API调用成功: daily, 耗时: 0.31秒, 返回 5677 条记录
2025-08-03 16:09:31 | 调用API: daily, 参数: {'ts_code': '000021.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:33 | 调用API: daily, 参数: {'ts_code': '000021.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:37 | 调用API: daily, 参数: {'ts_code': '000021.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:37 | 调用API: daily, 参数: {'ts_code': '000025.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:39 | 调用API: daily, 参数: {'ts_code': '000025.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:44 | 调用API: daily, 参数: {'ts_code': '000025.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:44 | 调用API: daily, 参数: {'ts_code': '000026.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:46 | 调用API: daily, 参数: {'ts_code': '000026.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:50 | 调用API: daily, 参数: {'ts_code': '000026.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:50 | 调用API: daily, 参数: {'ts_code': '000027.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:52 | 调用API: daily, 参数: {'ts_code': '000027.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:56 | 调用API: daily, 参数: {'ts_code': '000027.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:57 | 调用API: daily, 参数: {'ts_code': '000028.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:09:59 | 调用API: daily, 参数: {'ts_code': '000028.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:10:03 | 调用API: daily, 参数: {'ts_code': '000028.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:10:03 | 调用API: daily, 参数: {'ts_code': '000029.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:10:05 | 调用API: daily, 参数: {'ts_code': '000029.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:10:06 | API调用成功: daily, 耗时: 0.44秒, 返回 5124 条记录
2025-08-03 16:12:29 | 调用API: daily, 参数: {'ts_code': '000030.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:12:31 | 调用API: daily, 参数: {'ts_code': '000030.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:12:35 | 调用API: daily, 参数: {'ts_code': '000030.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:12:36 | 调用API: daily, 参数: {'ts_code': '000031.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:12:38 | 调用API: daily, 参数: {'ts_code': '000031.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:33:40 | 调用API: index_daily, 参数: {'ts_code': '000001.SH', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:33:40 | API调用成功: index_daily, 耗时: 0.46秒, 返回 6199 条记录
2025-08-03 16:36:45 | 调用API: index_daily, 参数: {'ts_code': '399001.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:36:45 | API调用成功: index_daily, 耗时: 0.40秒, 返回 6199 条记录
2025-08-03 16:39:36 | 调用API: index_daily, 参数: {'ts_code': '399006.SZ', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:39:36 | API调用成功: index_daily, 耗时: 0.34秒, 返回 3685 条记录
2025-08-03 16:41:22 | 调用API: index_daily, 参数: {'ts_code': '000300.SH', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:41:22 | API调用成功: index_daily, 耗时: 0.42秒, 返回 4999 条记录
2025-08-03 16:43:51 | 调用API: index_daily, 参数: {'ts_code': '000905.SH', 'start_date': '20000101', 'end_date': '20250803'}
2025-08-03 16:43:52 | API调用成功: index_daily, 耗时: 0.51秒, 返回 4999 条记录
2025-08-03 17:07:18 | 调用API: namechange, 参数: {}
2025-08-03 17:07:19 | API调用成功: namechange, 耗时: 0.37秒, 返回 10000 条记录
2025-08-03 17:16:37 | 调用API: namechange, 参数: {}
2025-08-03 17:16:37 | API调用成功: namechange, 耗时: 0.30秒, 返回 10000 条记录
2025-08-03 17:22:33 | 调用API: namechange, 参数: {}
2025-08-03 17:22:33 | 调用API: namechange, 参数: {}
2025-08-03 17:22:33 | API调用成功: namechange, 耗时: 0.40秒, 返回 10000 条记录
2025-08-03 17:22:33 | API调用成功: namechange, 耗时: 0.40秒, 返回 10000 条记录
2025-08-03 17:40:12 | 调用API: namechange, 参数: {}
2025-08-03 17:40:12 | API调用成功: namechange, 耗时: 0.33秒, 返回 10000 条记录
2025-08-03 22:19:31 | 调用API: daily_basic, 参数: {'trade_date': '20000104'}
2025-08-03 22:19:31 | 调用API: daily_basic, 参数: {'trade_date': '20000104'}
2025-08-03 22:19:31 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 913 条记录
2025-08-03 22:19:31 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 913 条记录
2025-08-03 22:19:56 | 调用API: daily_basic, 参数: {'trade_date': '20000105'}
2025-08-03 22:19:56 | 调用API: daily_basic, 参数: {'trade_date': '20000105'}
2025-08-03 22:19:56 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 917 条记录
2025-08-03 22:19:56 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 917 条记录
2025-08-03 22:20:20 | 调用API: daily_basic, 参数: {'trade_date': '20000106'}
2025-08-03 22:20:20 | 调用API: daily_basic, 参数: {'trade_date': '20000106'}
2025-08-03 22:20:21 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 920 条记录
2025-08-03 22:20:21 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 920 条记录
2025-08-03 22:20:46 | 调用API: daily_basic, 参数: {'trade_date': '20000107'}
2025-08-03 22:20:46 | 调用API: daily_basic, 参数: {'trade_date': '20000107'}
2025-08-03 22:20:46 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 923 条记录
2025-08-03 22:20:46 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 923 条记录
2025-08-03 22:21:12 | 调用API: daily_basic, 参数: {'trade_date': '20000110'}
2025-08-03 22:21:12 | 调用API: daily_basic, 参数: {'trade_date': '20000110'}
2025-08-03 22:21:12 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 917 条记录
2025-08-03 22:21:12 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 917 条记录
2025-08-03 22:21:37 | 调用API: daily_basic, 参数: {'trade_date': '20000111'}
2025-08-03 22:21:37 | 调用API: daily_basic, 参数: {'trade_date': '20000111'}
2025-08-03 22:21:38 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 917 条记录
2025-08-03 22:21:38 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 917 条记录
2025-08-03 22:22:06 | 调用API: daily_basic, 参数: {'trade_date': '20000112'}
2025-08-03 22:22:06 | 调用API: daily_basic, 参数: {'trade_date': '20000112'}
2025-08-03 22:22:06 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 921 条记录
2025-08-03 22:22:06 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 921 条记录
2025-08-03 22:22:28 | 调用API: daily_basic, 参数: {'trade_date': '20000113'}
2025-08-03 22:22:28 | 调用API: daily_basic, 参数: {'trade_date': '20000113'}
2025-08-03 22:22:29 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 923 条记录
2025-08-03 22:22:29 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 923 条记录
2025-08-03 22:22:52 | 调用API: daily_basic, 参数: {'trade_date': '20000114'}
2025-08-03 22:22:52 | 调用API: daily_basic, 参数: {'trade_date': '20000114'}
2025-08-03 22:22:52 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 925 条记录
2025-08-03 22:22:52 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 925 条记录
2025-08-03 22:23:15 | 调用API: daily_basic, 参数: {'trade_date': '20000117'}
2025-08-03 22:23:15 | 调用API: daily_basic, 参数: {'trade_date': '20000117'}
2025-08-03 22:23:15 | API调用成功: daily_basic, 耗时: 0.25秒, 返回 920 条记录
2025-08-03 22:23:15 | API调用成功: daily_basic, 耗时: 0.25秒, 返回 920 条记录
2025-08-03 22:23:37 | 调用API: daily_basic, 参数: {'trade_date': '20000118'}
2025-08-03 22:23:37 | 调用API: daily_basic, 参数: {'trade_date': '20000118'}
2025-08-03 22:23:37 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 917 条记录
2025-08-03 22:23:37 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 917 条记录
2025-08-03 22:24:00 | 调用API: daily_basic, 参数: {'trade_date': '20000119'}
2025-08-03 22:24:00 | 调用API: daily_basic, 参数: {'trade_date': '20000119'}
2025-08-03 22:24:00 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 921 条记录
2025-08-03 22:24:00 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 921 条记录
2025-08-03 22:24:23 | 调用API: daily_basic, 参数: {'trade_date': '20000120'}
2025-08-03 22:24:23 | 调用API: daily_basic, 参数: {'trade_date': '20000120'}
2025-08-03 22:24:23 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 922 条记录
2025-08-03 22:24:23 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 922 条记录
2025-08-03 22:24:52 | 调用API: daily_basic, 参数: {'trade_date': '20000121'}
2025-08-03 22:24:52 | 调用API: daily_basic, 参数: {'trade_date': '20000121'}
2025-08-03 22:24:52 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 927 条记录
2025-08-03 22:24:52 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 927 条记录
2025-08-03 22:25:21 | 调用API: daily_basic, 参数: {'trade_date': '20000124'}
2025-08-03 22:25:21 | 调用API: daily_basic, 参数: {'trade_date': '20000124'}
2025-08-03 22:25:21 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 922 条记录
2025-08-03 22:25:21 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 922 条记录
2025-08-03 22:25:47 | 调用API: daily_basic, 参数: {'trade_date': '20000125'}
2025-08-03 22:25:47 | 调用API: daily_basic, 参数: {'trade_date': '20000125'}
2025-08-03 22:25:47 | API调用成功: daily_basic, 耗时: 0.19秒, 返回 923 条记录
2025-08-03 22:25:47 | API调用成功: daily_basic, 耗时: 0.19秒, 返回 923 条记录
2025-08-03 22:26:14 | 调用API: daily_basic, 参数: {'trade_date': '20000126'}
2025-08-03 22:26:14 | 调用API: daily_basic, 参数: {'trade_date': '20000126'}
2025-08-03 22:26:15 | API调用成功: daily_basic, 耗时: 0.28秒, 返回 921 条记录
2025-08-03 22:26:15 | API调用成功: daily_basic, 耗时: 0.28秒, 返回 921 条记录
2025-08-03 22:26:42 | 调用API: daily_basic, 参数: {'trade_date': '20000127'}
2025-08-03 22:26:42 | 调用API: daily_basic, 参数: {'trade_date': '20000127'}
2025-08-03 22:26:42 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 926 条记录
2025-08-03 22:26:42 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 926 条记录
2025-08-03 22:27:11 | 调用API: daily_basic, 参数: {'trade_date': '20000128'}
2025-08-03 22:27:11 | 调用API: daily_basic, 参数: {'trade_date': '20000128'}
2025-08-03 22:27:11 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 925 条记录
2025-08-03 22:27:11 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 925 条记录
2025-08-03 22:27:36 | 调用API: daily_basic, 参数: {'trade_date': '20000214'}
2025-08-03 22:27:36 | 调用API: daily_basic, 参数: {'trade_date': '20000214'}
2025-08-03 22:27:36 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 920 条记录
2025-08-03 22:27:36 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 920 条记录
2025-08-03 22:27:58 | 调用API: daily_basic, 参数: {'trade_date': '20000215'}
2025-08-03 22:27:58 | 调用API: daily_basic, 参数: {'trade_date': '20000215'}
2025-08-03 22:27:58 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 921 条记录
2025-08-03 22:27:58 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 921 条记录
2025-08-03 22:28:24 | 调用API: daily_basic, 参数: {'trade_date': '20000216'}
2025-08-03 22:28:24 | 调用API: daily_basic, 参数: {'trade_date': '20000216'}
2025-08-03 22:28:24 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 925 条记录
2025-08-03 22:28:24 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 925 条记录
2025-08-03 22:29:00 | 调用API: daily_basic, 参数: {'trade_date': '20000217'}
2025-08-03 22:29:00 | 调用API: daily_basic, 参数: {'trade_date': '20000217'}
2025-08-03 22:29:00 | API调用成功: daily_basic, 耗时: 0.30秒, 返回 925 条记录
2025-08-03 22:29:00 | API调用成功: daily_basic, 耗时: 0.30秒, 返回 925 条记录
2025-08-03 22:29:33 | 调用API: daily_basic, 参数: {'trade_date': '20000218'}
2025-08-03 22:29:33 | 调用API: daily_basic, 参数: {'trade_date': '20000218'}
2025-08-03 22:29:33 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 921 条记录
2025-08-03 22:29:33 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 921 条记录
2025-08-03 22:29:56 | 调用API: daily_basic, 参数: {'trade_date': '20000221'}
2025-08-03 22:29:56 | 调用API: daily_basic, 参数: {'trade_date': '20000221'}
2025-08-03 22:29:56 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 923 条记录
2025-08-03 22:29:56 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 923 条记录
2025-08-03 22:30:19 | 调用API: daily_basic, 参数: {'trade_date': '20000222'}
2025-08-03 22:30:19 | 调用API: daily_basic, 参数: {'trade_date': '20000222'}
2025-08-03 22:30:19 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 924 条记录
2025-08-03 22:30:19 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 924 条记录
2025-08-03 22:30:42 | 调用API: daily_basic, 参数: {'trade_date': '20000223'}
2025-08-03 22:30:42 | 调用API: daily_basic, 参数: {'trade_date': '20000223'}
2025-08-03 22:30:42 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 928 条记录
2025-08-03 22:30:42 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 928 条记录
2025-08-03 22:31:03 | 调用API: daily_basic, 参数: {'trade_date': '20000224'}
2025-08-03 22:31:03 | 调用API: daily_basic, 参数: {'trade_date': '20000224'}
2025-08-03 22:31:04 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 926 条记录
2025-08-03 22:31:04 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 926 条记录
2025-08-03 22:31:26 | 调用API: daily_basic, 参数: {'trade_date': '20000225'}
2025-08-03 22:31:26 | 调用API: daily_basic, 参数: {'trade_date': '20000225'}
2025-08-03 22:31:26 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 928 条记录
2025-08-03 22:31:26 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 928 条记录
2025-08-03 22:31:48 | 调用API: daily_basic, 参数: {'trade_date': '20000228'}
2025-08-03 22:31:48 | 调用API: daily_basic, 参数: {'trade_date': '20000228'}
2025-08-03 22:31:48 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 915 条记录
2025-08-03 22:31:48 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 915 条记录
2025-08-03 22:32:10 | 调用API: daily_basic, 参数: {'trade_date': '20000229'}
2025-08-03 22:32:10 | 调用API: daily_basic, 参数: {'trade_date': '20000229'}
2025-08-03 22:32:10 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 920 条记录
2025-08-03 22:32:10 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 920 条记录
2025-08-03 22:32:33 | 调用API: daily_basic, 参数: {'trade_date': '20000301'}
2025-08-03 22:32:33 | 调用API: daily_basic, 参数: {'trade_date': '20000301'}
2025-08-03 22:32:33 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 924 条记录
2025-08-03 22:32:33 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 924 条记录
2025-08-03 22:32:55 | 调用API: daily_basic, 参数: {'trade_date': '20000302'}
2025-08-03 22:32:55 | 调用API: daily_basic, 参数: {'trade_date': '20000302'}
2025-08-03 22:32:56 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 928 条记录
2025-08-03 22:32:56 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 928 条记录
2025-08-03 22:33:18 | 调用API: daily_basic, 参数: {'trade_date': '20000303'}
2025-08-03 22:33:18 | 调用API: daily_basic, 参数: {'trade_date': '20000303'}
2025-08-03 22:33:18 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 934 条记录
2025-08-03 22:33:18 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 934 条记录
2025-08-03 22:33:41 | 调用API: daily_basic, 参数: {'trade_date': '20000306'}
2025-08-03 22:33:41 | 调用API: daily_basic, 参数: {'trade_date': '20000306'}
2025-08-03 22:33:41 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 928 条记录
2025-08-03 22:33:41 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 928 条记录
2025-08-03 22:34:04 | 调用API: daily_basic, 参数: {'trade_date': '20000307'}
2025-08-03 22:34:04 | 调用API: daily_basic, 参数: {'trade_date': '20000307'}
2025-08-03 22:34:04 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 931 条记录
2025-08-03 22:34:04 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 931 条记录
2025-08-03 22:34:26 | 调用API: daily_basic, 参数: {'trade_date': '20000308'}
2025-08-03 22:34:26 | 调用API: daily_basic, 参数: {'trade_date': '20000308'}
2025-08-03 22:34:26 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 930 条记录
2025-08-03 22:34:26 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 930 条记录
2025-08-03 22:34:48 | 调用API: daily_basic, 参数: {'trade_date': '20000309'}
2025-08-03 22:34:48 | 调用API: daily_basic, 参数: {'trade_date': '20000309'}
2025-08-03 22:34:49 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 931 条记录
2025-08-03 22:34:49 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 931 条记录
2025-08-03 22:35:12 | 调用API: daily_basic, 参数: {'trade_date': '20000310'}
2025-08-03 22:35:12 | 调用API: daily_basic, 参数: {'trade_date': '20000310'}
2025-08-03 22:35:12 | API调用成功: daily_basic, 耗时: 0.25秒, 返回 933 条记录
2025-08-03 22:35:12 | API调用成功: daily_basic, 耗时: 0.25秒, 返回 933 条记录
2025-08-03 22:35:34 | 调用API: daily_basic, 参数: {'trade_date': '20000313'}
2025-08-03 22:35:34 | 调用API: daily_basic, 参数: {'trade_date': '20000313'}
2025-08-03 22:35:34 | API调用成功: daily_basic, 耗时: 0.30秒, 返回 930 条记录
2025-08-03 22:35:34 | API调用成功: daily_basic, 耗时: 0.30秒, 返回 930 条记录
2025-08-03 22:35:56 | 调用API: daily_basic, 参数: {'trade_date': '20000314'}
2025-08-03 22:35:56 | 调用API: daily_basic, 参数: {'trade_date': '20000314'}
2025-08-03 22:35:57 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 931 条记录
2025-08-03 22:35:57 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 931 条记录
2025-08-03 22:36:19 | 调用API: daily_basic, 参数: {'trade_date': '20000315'}
2025-08-03 22:36:19 | 调用API: daily_basic, 参数: {'trade_date': '20000315'}
2025-08-03 22:36:20 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 932 条记录
2025-08-03 22:36:20 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 932 条记录
2025-08-03 22:36:42 | 调用API: daily_basic, 参数: {'trade_date': '20000316'}
2025-08-03 22:36:42 | 调用API: daily_basic, 参数: {'trade_date': '20000316'}
2025-08-03 22:36:42 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 924 条记录
2025-08-03 22:36:42 | API调用成功: daily_basic, 耗时: 0.24秒, 返回 924 条记录
2025-08-03 22:37:04 | 调用API: daily_basic, 参数: {'trade_date': '20000317'}
2025-08-03 22:37:04 | 调用API: daily_basic, 参数: {'trade_date': '20000317'}
2025-08-03 22:37:04 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 932 条记录
2025-08-03 22:37:04 | API调用成功: daily_basic, 耗时: 0.18秒, 返回 932 条记录
2025-08-03 22:37:32 | 调用API: daily_basic, 参数: {'trade_date': '20000320'}
2025-08-03 22:37:32 | 调用API: daily_basic, 参数: {'trade_date': '20000320'}
2025-08-03 22:37:32 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 924 条记录
2025-08-03 22:37:32 | API调用成功: daily_basic, 耗时: 0.22秒, 返回 924 条记录
2025-08-03 22:37:59 | 调用API: daily_basic, 参数: {'trade_date': '20000321'}
2025-08-03 22:37:59 | 调用API: daily_basic, 参数: {'trade_date': '20000321'}
2025-08-03 22:38:00 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 929 条记录
2025-08-03 22:38:00 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 929 条记录
2025-08-03 22:46:30 | 调用API: daily_basic, 参数: {'trade_date': '20000104'}
2025-08-03 22:46:30 | API调用成功: daily_basic, 耗时: 0.23秒, 返回 913 条记录
2025-08-03 22:46:57 | 调用API: daily_basic, 参数: {'trade_date': '20000105'}
2025-08-03 22:46:57 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 917 条记录
2025-08-03 22:48:00 | 调用API: daily_basic, 参数: {'trade_date': '20000104'}
2025-08-03 22:48:00 | 调用API: daily_basic, 参数: {'trade_date': '20000104'}
2025-08-03 22:48:00 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 913 条记录
2025-08-03 22:48:00 | API调用成功: daily_basic, 耗时: 0.20秒, 返回 913 条记录
2025-08-03 22:48:28 | 调用API: daily_basic, 参数: {'trade_date': '20000105'}
2025-08-03 22:48:28 | 调用API: daily_basic, 参数: {'trade_date': '20000105'}
2025-08-03 22:48:28 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 917 条记录
2025-08-03 22:48:28 | API调用成功: daily_basic, 耗时: 0.21秒, 返回 917 条记录
2025-08-03 22:48:57 | 调用API: daily_basic, 参数: {'trade_date': '20000106'}
2025-08-03 22:48:57 | 调用API: daily_basic, 参数: {'trade_date': '20000106'}
2025-08-03 22:48:57 | API调用成功: daily_basic, 耗时: 0.29秒, 返回 920 条记录
2025-08-03 22:48:57 | API调用成功: daily_basic, 耗时: 0.29秒, 返回 920 条记录
2025-08-03 22:49:30 | 调用API: daily_basic, 参数: {'trade_date': '20000107'}
2025-08-03 22:49:30 | 调用API: daily_basic, 参数: {'trade_date': '20000107'}
2025-08-03 22:49:30 | API调用成功: daily_basic, 耗时: 0.19秒, 返回 923 条记录
2025-08-03 22:49:30 | API调用成功: daily_basic, 耗时: 0.19秒, 返回 923 条记录
2025-08-03 22:50:08 | 调用API: daily_basic, 参数: {'trade_date': '20000110'}
2025-08-03 22:50:08 | 调用API: daily_basic, 参数: {'trade_date': '20000110'}
2025-08-03 22:50:08 | API调用成功: daily_basic, 耗时: 0.29秒, 返回 917 条记录
2025-08-03 22:50:08 | API调用成功: daily_basic, 耗时: 0.29秒, 返回 917 条记录
2025-08-03 22:50:41 | 调用API: daily_basic, 参数: {'trade_date': '20000111'}
2025-08-03 22:50:41 | 调用API: daily_basic, 参数: {'trade_date': '20000111'}
2025-08-03 22:50:41 | API调用成功: daily_basic, 耗时: 0.27秒, 返回 917 条记录
2025-08-03 22:50:41 | API调用成功: daily_basic, 耗时: 0.27秒, 返回 917 条记录
2025-08-03 23:21:48 | 调用API: daily_basic, 参数: {'trade_date': '20250702'}
2025-08-03 23:21:48 | 调用API: daily_basic, 参数: {'trade_date': '20250702'}
2025-08-03 23:21:48 | API调用成功: daily_basic, 耗时: 0.67秒, 返回 5403 条记录
2025-08-03 23:21:48 | API调用成功: daily_basic, 耗时: 0.67秒, 返回 5403 条记录
2025-08-03 23:24:25 | 调用API: daily_basic, 参数: {'trade_date': '20250703'}
2025-08-03 23:24:25 | 调用API: daily_basic, 参数: {'trade_date': '20250703'}
2025-08-03 23:24:25 | API调用成功: daily_basic, 耗时: 0.50秒, 返回 5403 条记录
2025-08-03 23:24:25 | API调用成功: daily_basic, 耗时: 0.50秒, 返回 5403 条记录
2025-08-03 23:27:10 | 调用API: daily_basic, 参数: {'trade_date': '20250704'}
2025-08-03 23:27:10 | 调用API: daily_basic, 参数: {'trade_date': '20250704'}
2025-08-03 23:27:10 | API调用成功: daily_basic, 耗时: 0.44秒, 返回 5405 条记录
2025-08-03 23:27:10 | API调用成功: daily_basic, 耗时: 0.44秒, 返回 5405 条记录
2025-08-03 23:30:10 | 调用API: daily_basic, 参数: {'start_date': '20250702', 'end_date': '20250803'}
2025-08-03 23:30:10 | 调用API: daily_basic, 参数: {'start_date': '20250702', 'end_date': '20250803'}
2025-08-03 23:30:11 | API调用成功: daily_basic, 耗时: 0.63秒, 返回 6000 条记录
2025-08-03 23:30:11 | API调用成功: daily_basic, 耗时: 0.63秒, 返回 6000 条记录
