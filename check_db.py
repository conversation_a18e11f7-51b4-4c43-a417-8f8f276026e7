#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd

def check_database():
    """检查数据库状态"""
    try:
        # 连接数据库
        conn = sqlite3.connect('data/tushare_data.db')
        
        # 查看表列表
        tables = pd.read_sql('SELECT name FROM sqlite_master WHERE type="table"', conn)
        print('数据库中的表:')
        print(tables)
        print()
        
        # 查看股票基本信息表的记录数
        try:
            stock_count = pd.read_sql('SELECT COUNT(*) as count FROM stock_basic', conn)
            print(f'股票基本信息记录数: {stock_count.iloc[0]["count"]}')
            
            # 查看前5条记录
            sample_data = pd.read_sql('SELECT * FROM stock_basic LIMIT 5', conn)
            print('股票基本信息样本数据:')
            print(sample_data)
            print()
        except Exception as e:
            print(f'查询股票基本信息失败: {e}')
        
        # 查看交易日历表的记录数
        try:
            cal_count = pd.read_sql('SELECT COUNT(*) as count FROM trade_cal', conn)
            print(f'交易日历记录数: {cal_count.iloc[0]["count"]}')
            
            # 查看最新的5条记录
            sample_cal = pd.read_sql('SELECT * FROM trade_cal ORDER BY cal_date DESC LIMIT 5', conn)
            print('交易日历最新数据:')
            print(sample_cal)
            print()
        except Exception as e:
            print(f'查询交易日历失败: {e}')
        
        # 查看下载进度
        try:
            progress = pd.read_sql('SELECT * FROM download_progress ORDER BY created_at DESC', conn)
            print('下载进度:')
            print(progress)
        except Exception as e:
            print(f'查询下载进度失败: {e}')
        
        conn.close()
        
    except Exception as e:
        print(f'数据库连接失败: {e}')

if __name__ == '__main__':
    check_database()
