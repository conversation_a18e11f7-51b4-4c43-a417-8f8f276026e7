#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web下载管理器
处理来自Web界面的下载请求，提供实时状态更新和操作日志
"""

import os
import sys
import sqlite3
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.core.config_manager import ConfigManager
from src.core.logger import setup_logger
from src.core.database import DatabaseManager
from src.downloader.data_downloader import DataDownloader
from src.utils.progress_tracker import ProgressTracker

class WebDownloadManager:
    """Web下载管理器"""
    
    def __init__(self):
        """初始化Web下载管理器"""
        self.config = ConfigManager('config.yaml')
        self.logger = setup_logger(self.config)
        self.db_manager = DatabaseManager(self.config)
        self.db_manager.initialize()
        self.progress_tracker = ProgressTracker(self.db_manager)
        self.downloader = DataDownloader(self.config, self.db_manager, self.progress_tracker)
        
        # 下载状态管理
        self.download_status = {}  # 存储当前下载状态
        self.operation_logs = []   # 操作日志
        self.active_tasks = {}     # 活动任务
        self.lock = threading.Lock()
        
        # 表名映射
        self.table_mapping = {
            # 基础数据
            'stock_basic': {
                'chinese_name': '股票基础信息',
                'downloader_method': 'download_stock_basic',
                'data_type': 'basic'
            },
            'trade_cal': {
                'chinese_name': '交易日历',
                'downloader_method': 'download_trade_calendar',
                'data_type': 'basic'
            },
            'stock_company': {
                'chinese_name': '上市公司信息',
                'downloader_method': 'download_stock_company',
                'data_type': 'basic'
            },
            'hs_const': {
                'chinese_name': '沪深港通成份股',
                'downloader_method': 'download_hs_const',
                'data_type': 'basic'
            },
            'concept': {
                'chinese_name': '概念股分类',
                'downloader_method': 'download_concept',
                'data_type': 'basic'
            },
            'concept_detail': {
                'chinese_name': '概念股明细',
                'downloader_method': 'download_concept_detail',
                'data_type': 'basic'
            },
            'namechange': {
                'chinese_name': '股票曾用名',
                'downloader_method': 'download_namechange',
                'data_type': 'basic'
            },
            'new_share': {
                'chinese_name': '新股列表',
                'downloader_method': 'download_new_share',
                'data_type': 'basic'
            },
            # 市场数据
            'daily': {
                'chinese_name': '日线行情',
                'downloader_method': 'download_daily_data',
                'data_type': 'daily'
            },
            'daily_basic': {
                'chinese_name': '每日指标',
                'downloader_method': 'download_daily_basic',
                'data_type': 'daily'
            },
            'adj_factor': {
                'chinese_name': '复权因子',
                'downloader_method': 'download_adj_factor',
                'data_type': 'daily'
            },
            'suspend_d': {
                'chinese_name': '停牌信息',
                'downloader_method': 'download_suspend_data',
                'data_type': 'daily'
            },
            'moneyflow': {
                'chinese_name': '资金流向',
                'downloader_method': 'download_moneyflow',
                'data_type': 'daily'
            },
            'stk_limit': {
                'chinese_name': '涨跌停价格',
                'downloader_method': 'download_stk_limit',
                'data_type': 'daily'
            },
            # 财务数据
            'income': {
                'chinese_name': '利润表',
                'downloader_method': 'download_income_data',
                'data_type': 'financial'
            },
            'balancesheet': {
                'chinese_name': '资产负债表',
                'downloader_method': 'download_balance_sheet',
                'data_type': 'financial'
            },
            'cashflow': {
                'chinese_name': '现金流量表',
                'downloader_method': 'download_cashflow_data',
                'data_type': 'financial'
            },
            'fina_indicator': {
                'chinese_name': '财务指标',
                'downloader_method': 'download_financial_indicators',
                'data_type': 'financial'
            },
            'forecast': {
                'chinese_name': '业绩预告',
                'downloader_method': 'download_forecast_data',
                'data_type': 'financial'
            },
            'express': {
                'chinese_name': '业绩快报',
                'downloader_method': 'download_express_data',
                'data_type': 'financial'
            },
            'dividend': {
                'chinese_name': '分红送股',
                'downloader_method': 'download_dividend_data',
                'data_type': 'financial'
            },
            'fina_audit': {
                'chinese_name': '财务审计意见',
                'downloader_method': 'download_fina_audit',
                'data_type': 'financial'
            },
            'fina_mainbz': {
                'chinese_name': '主营业务构成',
                'downloader_method': 'download_fina_mainbz',
                'data_type': 'financial'
            },
            # 指数数据
            'index_basic': {
                'chinese_name': '指数基础信息',
                'downloader_method': 'download_index_basic',
                'data_type': 'index'
            },
            'index_daily': {
                'chinese_name': '指数日线行情',
                'downloader_method': 'download_index_daily',
                'data_type': 'index'
            },
            'index_dailybasic': {
                'chinese_name': '指数每日指标',
                'downloader_method': 'download_index_daily_basic',
                'data_type': 'index'
            },
            'index_weight': {
                'chinese_name': '指数成分权重',
                'downloader_method': 'download_index_weight',
                'data_type': 'index'
            }
        }
    
    def get_db_connection(self):
        """获取数据库连接"""
        db_path = 'data/tushare_data.db'
        if not os.path.exists(db_path):
            return None
        return sqlite3.connect(db_path)
    
    def log_operation(self, operation: str, table_name: str, status: str, message: str = ""):
        """记录操作日志"""
        with self.lock:
            log_entry = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'operation': operation,
                'table_name': table_name,
                'chinese_name': self.table_mapping.get(table_name, {}).get('chinese_name', table_name),
                'status': status,
                'message': message
            }
            self.operation_logs.append(log_entry)
            
            # 只保留最近100条日志
            if len(self.operation_logs) > 100:
                self.operation_logs = self.operation_logs[-100:]
    
    def get_table_status(self, table_name: str) -> str:
        """获取表的状态"""
        conn = self.get_db_connection()
        if not conn:
            return 'unknown'
        
        try:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                return 'not_exists'
            
            # 获取记录数
            cursor.execute(f'SELECT COUNT(*) FROM {table_name}')
            count = cursor.fetchone()[0]
            
            if count == 0:
                return 'empty'
            
            # 检查是否正在下载
            if table_name in self.active_tasks:
                return 'downloading'
            
            # 检查是否需要更新（基于最后更新时间）
            date_columns = ['trade_date', 'cal_date', 'end_date', 'ann_date']
            latest_date = None
            
            for date_col in date_columns:
                try:
                    cursor.execute(f'PRAGMA table_info({table_name})')
                    columns = [col[1] for col in cursor.fetchall()]
                    if date_col in columns:
                        cursor.execute(f'SELECT MAX({date_col}) FROM {table_name}')
                        latest_date = cursor.fetchone()[0]
                        break
                except:
                    continue
            
            if latest_date:
                try:
                    latest_datetime = datetime.strptime(latest_date, '%Y%m%d')
                    days_old = (datetime.now() - latest_datetime).days
                    
                    # 如果数据超过7天没更新，标记为需要更新
                    if days_old > 7:
                        return 'needs_update'
                except:
                    pass
            
            return 'downloaded'
            
        except Exception as e:
            self.logger.error(f"获取表状态失败: {e}")
            return 'error'
        finally:
            conn.close()
    
    def download_single_table(self, table_name: str, mode: str = 'full',
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None) -> Dict[str, Any]:
        """下载单个表"""
        if table_name not in self.table_mapping:
            return {
                'success': False,
                'message': f'不支持的表名: {table_name}'
            }
        
        # 检查是否已在下载中
        if table_name in self.active_tasks:
            return {
                'success': False,
                'message': f'表 {table_name} 正在下载中，请稍后再试'
            }
        
        # 创建下载任务
        task_id = f"{table_name}_{int(time.time())}"
        
        def download_task():
            try:
                with self.lock:
                    self.active_tasks[table_name] = task_id
                    self.download_status[task_id] = {
                        'table_name': table_name,
                        'chinese_name': self.table_mapping[table_name]['chinese_name'],
                        'status': 'downloading',
                        'progress': 0,
                        'message': '开始下载...',
                        'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                self.log_operation('download', table_name, 'started', f'开始{mode}下载')

                # 根据表类型调用相应的下载方法
                if table_name == 'stock_basic':
                    self.downloader.basic_downloader.download_stock_basic()
                elif table_name == 'trade_cal':
                    self.downloader.basic_downloader.download_trade_calendar()
                elif table_name == 'stock_company':
                    self.downloader.basic_downloader.download_stock_company()
                elif table_name == 'daily':
                    # 获取股票列表
                    stock_list = self._get_stock_list()
                    if mode == 'incremental' and not start_date:
                        download_start_date = self._get_last_trade_date(table_name)
                    else:
                        download_start_date = start_date
                    self.downloader.market_downloader.download_daily_data(stock_list, start_date=download_start_date)
                else:
                    # 其他表的下载逻辑
                    self._download_other_table(table_name, mode, start_date, end_date)
                
                # 更新状态为完成
                with self.lock:
                    if task_id in self.download_status:
                        self.download_status[task_id].update({
                            'status': 'completed',
                            'progress': 100,
                            'message': '下载完成',
                            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                
                self.log_operation('download', table_name, 'completed', '下载成功完成')
                
            except Exception as e:
                error_msg = str(e)
                self.logger.error(f"下载表 {table_name} 失败: {error_msg}")
                
                # 更新状态为失败
                with self.lock:
                    if task_id in self.download_status:
                        self.download_status[task_id].update({
                            'status': 'failed',
                            'message': f'下载失败: {error_msg}',
                            'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        })
                
                self.log_operation('download', table_name, 'failed', error_msg)
                
            finally:
                # 清理活动任务
                with self.lock:
                    if table_name in self.active_tasks:
                        del self.active_tasks[table_name]
        
        # 启动下载线程
        thread = threading.Thread(target=download_task)
        thread.daemon = True
        thread.start()
        
        return {
            'success': True,
            'task_id': task_id,
            'message': f'开始下载 {self.table_mapping[table_name]["chinese_name"]}'
        }
    
    def _get_stock_list(self) -> List[str]:
        """获取股票列表"""
        conn = self.get_db_connection()
        if not conn:
            return []

        try:
            cursor = conn.cursor()
            cursor.execute("SELECT ts_code FROM stock_basic WHERE list_status='L'")
            return [row[0] for row in cursor.fetchall()]
        except:
            return []
        finally:
            conn.close()

    def _get_index_list(self) -> List[str]:
        """获取指数列表"""
        conn = self.get_db_connection()
        if not conn:
            # 如果没有指数基础数据，返回一些常用指数
            return ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH', '000905.SH']

        try:
            cursor = conn.cursor()
            cursor.execute("SELECT ts_code FROM index_basic")
            index_list = [row[0] for row in cursor.fetchall()]
            # 如果没有数据，返回常用指数
            if not index_list:
                return ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH', '000905.SH']
            return index_list
        except:
            # 如果表不存在，返回常用指数
            return ['000001.SH', '399001.SZ', '399006.SZ', '000300.SH', '000905.SH']
        finally:
            conn.close()
    
    def _get_last_trade_date(self, table_name: str) -> Optional[str]:
        """获取表中最后的交易日期"""
        conn = self.get_db_connection()
        if not conn:
            return None
        
        try:
            cursor = conn.cursor()
            cursor.execute(f"SELECT MAX(trade_date) FROM {table_name}")
            result = cursor.fetchone()
            return result[0] if result and result[0] else None
        except:
            return None
        finally:
            conn.close()
    
    def _download_other_table(self, table_name: str, mode: str,
                             start_date: Optional[str] = None,
                             end_date: Optional[str] = None):
        """下载其他表的通用方法"""
        table_info = self.table_mapping.get(table_name)
        if not table_info:
            raise ValueError(f"不支持的表名: {table_name}")

        data_type = table_info['data_type']
        downloader_method = table_info['downloader_method']

        # 根据数据类型选择相应的下载器
        if data_type == 'basic':
            downloader = self.downloader.basic_downloader
        elif data_type == 'daily':
            downloader = self.downloader.market_downloader
        elif data_type == 'financial':
            downloader = self.downloader.financial_downloader
        elif data_type == 'index':
            downloader = self.downloader.index_downloader
        else:
            raise ValueError(f"不支持的数据类型: {data_type}")

        # 调用相应的下载方法
        if hasattr(downloader, downloader_method):
            method = getattr(downloader, downloader_method)

            # 根据表类型和模式调用不同的参数
            if table_name == 'index_daily':
                # 指数日线数据需要指数列表
                index_list = self._get_index_list()
                if mode == 'incremental':
                    start_date = self._get_last_trade_date(table_name)
                    method(index_list, start_date=start_date)
                else:
                    method(index_list)
            elif table_name == 'index_basic':
                # 指数基础信息
                method()
            elif data_type == 'financial':
                # 财务数据需要股票列表
                stock_list = self._get_stock_list()
                download_start_date = start_date
                if mode == 'incremental' and not download_start_date:
                    download_start_date = self._get_last_trade_date(table_name)

                if download_start_date or end_date:
                    method(stock_list, start_date=download_start_date, end_date=end_date)
                else:
                    method(stock_list)
            elif table_name in ['daily_basic', 'moneyflow', 'stk_limit', 'adj_factor', 'suspend_d']:
                # 这些市场数据表需要股票列表
                stock_list = self._get_stock_list()
                download_start_date = start_date
                if mode == 'incremental' and not download_start_date:
                    download_start_date = self._get_last_trade_date(table_name)

                if download_start_date or end_date:
                    method(stock_list, start_date=download_start_date, end_date=end_date)
                else:
                    method(stock_list)
            else:
                # 其他基础数据
                method()
        else:
            raise NotImplementedError(f"下载器中未找到方法: {downloader_method}")
    
    def get_download_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取下载状态"""
        with self.lock:
            return self.download_status.get(task_id)
    
    def get_all_download_status(self) -> Dict[str, Any]:
        """获取所有下载状态"""
        with self.lock:
            return dict(self.download_status)
    
    def get_operation_logs(self, limit: int = 50) -> List[Dict[str, Any]]:
        """获取操作日志"""
        with self.lock:
            return self.operation_logs[-limit:] if limit > 0 else self.operation_logs.copy()
    
    def cancel_download(self, task_id: str) -> Dict[str, Any]:
        """取消下载任务"""
        with self.lock:
            if task_id in self.download_status:
                status = self.download_status[task_id]
                if status['status'] == 'downloading':
                    status.update({
                        'status': 'cancelled',
                        'message': '用户取消下载',
                        'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    })

                    # 从活动任务中移除
                    table_name = status['table_name']
                    if table_name in self.active_tasks:
                        del self.active_tasks[table_name]

                    self.log_operation('cancel', table_name, 'cancelled', '用户取消下载')

                    return {'success': True, 'message': '下载已取消'}
                else:
                    return {'success': False, 'message': '任务不在下载状态，无法取消'}
            else:
                return {'success': False, 'message': '任务不存在'}

    def update_all_tables(self) -> Dict[str, Any]:
        """更新所有已下载的表"""
        try:
            # 获取所有需要更新的表
            tables_to_update = []
            for table_name in self.table_mapping.keys():
                status = self.get_table_status(table_name)
                if status in ['downloaded', 'needs_update']:
                    tables_to_update.append(table_name)

            if not tables_to_update:
                return {
                    'success': False,
                    'message': '没有需要更新的表'
                }

            # 创建批量更新任务
            batch_task_id = f"batch_update_{int(time.time())}"

            def batch_update_task():
                try:
                    with self.lock:
                        self.download_status[batch_task_id] = {
                            'table_name': 'batch_update',
                            'chinese_name': '批量更新',
                            'status': 'downloading',
                            'progress': 0,
                            'message': f'开始更新 {len(tables_to_update)} 个表...',
                            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'total_tables': len(tables_to_update),
                            'completed_tables': 0
                        }

                    self.log_operation('batch_update', 'all', 'started', f'开始批量更新 {len(tables_to_update)} 个表')

                    completed = 0
                    failed = 0

                    for i, table_name in enumerate(tables_to_update):
                        try:
                            # 更新进度
                            with self.lock:
                                if batch_task_id in self.download_status:
                                    self.download_status[batch_task_id].update({
                                        'progress': int((i / len(tables_to_update)) * 100),
                                        'message': f'正在更新 {self.table_mapping[table_name]["chinese_name"]}...',
                                        'completed_tables': completed
                                    })

                            # 下载表（增量模式）
                            result = self.download_single_table(table_name, 'incremental')
                            if result['success']:
                                completed += 1
                                self.log_operation('update', table_name, 'completed', '增量更新成功')
                            else:
                                failed += 1
                                self.log_operation('update', table_name, 'failed', result['message'])

                            # 等待一段时间避免过于频繁的请求
                            time.sleep(1)

                        except Exception as e:
                            failed += 1
                            self.log_operation('update', table_name, 'failed', str(e))

                    # 更新最终状态
                    with self.lock:
                        if batch_task_id in self.download_status:
                            self.download_status[batch_task_id].update({
                                'status': 'completed',
                                'progress': 100,
                                'message': f'批量更新完成：成功 {completed} 个，失败 {failed} 个',
                                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'completed_tables': completed,
                                'failed_tables': failed
                            })

                    self.log_operation('batch_update', 'all', 'completed', f'批量更新完成：成功 {completed} 个，失败 {failed} 个')

                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"批量更新失败: {error_msg}")

                    with self.lock:
                        if batch_task_id in self.download_status:
                            self.download_status[batch_task_id].update({
                                'status': 'failed',
                                'message': f'批量更新失败: {error_msg}',
                                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })

                    self.log_operation('batch_update', 'all', 'failed', error_msg)

            # 启动批量更新线程
            thread = threading.Thread(target=batch_update_task)
            thread.daemon = True
            thread.start()

            return {
                'success': True,
                'task_id': batch_task_id,
                'message': f'开始批量更新 {len(tables_to_update)} 个表'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'启动批量更新失败: {str(e)}'
            }

    def download_missing_tables(self) -> Dict[str, Any]:
        """下载所有缺失的表"""
        try:
            # 获取所有缺失的表
            missing_tables = []
            for table_name in self.table_mapping.keys():
                status = self.get_table_status(table_name)
                if status in ['empty', 'not_exists']:
                    missing_tables.append(table_name)

            if not missing_tables:
                return {
                    'success': False,
                    'message': '没有缺失的表需要下载'
                }

            # 创建批量下载任务
            batch_task_id = f"batch_download_{int(time.time())}"

            def batch_download_task():
                try:
                    with self.lock:
                        self.download_status[batch_task_id] = {
                            'table_name': 'batch_download',
                            'chinese_name': '批量下载',
                            'status': 'downloading',
                            'progress': 0,
                            'message': f'开始下载 {len(missing_tables)} 个缺失的表...',
                            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'total_tables': len(missing_tables),
                            'completed_tables': 0
                        }

                    self.log_operation('batch_download', 'missing', 'started', f'开始批量下载 {len(missing_tables)} 个缺失的表')

                    completed = 0
                    failed = 0

                    for i, table_name in enumerate(missing_tables):
                        try:
                            # 更新进度
                            with self.lock:
                                if batch_task_id in self.download_status:
                                    self.download_status[batch_task_id].update({
                                        'progress': int((i / len(missing_tables)) * 100),
                                        'message': f'正在下载 {self.table_mapping[table_name]["chinese_name"]}...',
                                        'completed_tables': completed
                                    })

                            # 下载表（完整模式）
                            result = self.download_single_table(table_name, 'full')
                            if result['success']:
                                completed += 1
                                self.log_operation('download', table_name, 'completed', '下载成功')
                            else:
                                failed += 1
                                self.log_operation('download', table_name, 'failed', result['message'])

                            # 等待一段时间避免过于频繁的请求
                            time.sleep(2)

                        except Exception as e:
                            failed += 1
                            self.log_operation('download', table_name, 'failed', str(e))

                    # 更新最终状态
                    with self.lock:
                        if batch_task_id in self.download_status:
                            self.download_status[batch_task_id].update({
                                'status': 'completed',
                                'progress': 100,
                                'message': f'批量下载完成：成功 {completed} 个，失败 {failed} 个',
                                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                'completed_tables': completed,
                                'failed_tables': failed
                            })

                    self.log_operation('batch_download', 'missing', 'completed', f'批量下载完成：成功 {completed} 个，失败 {failed} 个')

                except Exception as e:
                    error_msg = str(e)
                    self.logger.error(f"批量下载失败: {error_msg}")

                    with self.lock:
                        if batch_task_id in self.download_status:
                            self.download_status[batch_task_id].update({
                                'status': 'failed',
                                'message': f'批量下载失败: {error_msg}',
                                'end_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                            })

                    self.log_operation('batch_download', 'missing', 'failed', error_msg)

            # 启动批量下载线程
            thread = threading.Thread(target=batch_download_task)
            thread.daemon = True
            thread.start()

            return {
                'success': True,
                'task_id': batch_task_id,
                'message': f'开始批量下载 {len(missing_tables)} 个缺失的表'
            }

        except Exception as e:
            return {
                'success': False,
                'message': f'启动批量下载失败: {str(e)}'
            }

# 全局实例
download_manager = WebDownloadManager()
