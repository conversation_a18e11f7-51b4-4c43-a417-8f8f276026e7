#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志系统模块
提供统一的日志记录功能
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional


def setup_logger(config, logger_name: Optional[str] = None):
    """设置日志系统"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.get_logging_config()
    storage_config = config.get_storage_config()
    
    # 日志级别
    log_level = log_config.get('level', 'INFO')
    
    # 日志目录
    log_dir = Path(storage_config.get('log_dir', 'logs'))
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 日志格式
    log_format = (
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
        "<level>{message}</level>"
    )
    
    # 控制台输出
    if log_config.get('console_output', True):
        logger.add(
            sys.stdout,
            format=log_format,
            level=log_level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
    
    # 文件输出 - 主日志文件
    main_log_file = log_dir / "tushare_downloader.log"
    logger.add(
        str(main_log_file),
        format=log_format,
        level=log_level,
        rotation=log_config.get('max_file_size', '10MB'),
        retention=log_config.get('backup_count', 5),
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    # 错误日志文件
    error_log_file = log_dir / "error.log"
    logger.add(
        str(error_log_file),
        format=log_format,
        level="ERROR",
        rotation=log_config.get('max_file_size', '10MB'),
        retention=log_config.get('backup_count', 5),
        compression="zip",
        backtrace=True,
        diagnose=True,
        encoding="utf-8"
    )
    
    # 下载进度日志文件
    progress_log_file = log_dir / "progress.log"
    logger.add(
        str(progress_log_file),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | {message}",
        level="INFO",
        rotation="1 day",
        retention=30,
        filter=lambda record: "PROGRESS" in record["extra"],
        encoding="utf-8"
    )
    
    # API调用日志文件
    api_log_file = log_dir / "api_calls.log"
    logger.add(
        str(api_log_file),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | {message}",
        level="DEBUG",
        rotation="1 day",
        retention=7,
        filter=lambda record: "API" in record["extra"],
        encoding="utf-8"
    )
    
    return logger


def get_progress_logger():
    """获取进度日志记录器"""
    return logger.bind(PROGRESS=True)


def get_api_logger():
    """获取API调用日志记录器"""
    return logger.bind(API=True)


class LoggerMixin:
    """日志记录器混入类"""
    
    @property
    def logger(self):
        """获取日志记录器"""
        return logger
    
    def log_progress(self, message: str):
        """记录进度日志"""
        get_progress_logger().info(message)
    
    def log_api_call(self, message: str):
        """记录API调用日志"""
        get_api_logger().debug(message)
    
    def log_error(self, message: str, exception: Optional[Exception] = None):
        """记录错误日志"""
        if exception:
            logger.exception(f"{message}: {exception}")
        else:
            logger.error(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        logger.warning(message)
    
    def log_info(self, message: str):
        """记录信息日志"""
        logger.info(message)
    
    def log_debug(self, message: str):
        """记录调试日志"""
        logger.debug(message)
