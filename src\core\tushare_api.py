#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
tushare API封装模块
提供统一的API调用接口，包含频率限制、错误处理、重试机制
"""

import time
import pandas as pd
import tushare as ts
from typing import Optional, Dict, Any, List
from datetime import datetime, timedelta
from retrying import retry
from loguru import logger

from .logger import LoggerMixin


class TushareAPI(LoggerMixin):
    """tushare API封装类"""
    
    def __init__(self, config):
        """初始化API客户端"""
        self.config = config
        self.ts_config = config.get_tushare_config()
        
        # 设置token
        self.token = self.ts_config.get('token')
        if not self.token:
            raise ValueError("tushare token未配置")
        
        ts.set_token(self.token)
        self.pro = ts.pro_api()
        
        # API调用配置
        self.timeout = self.ts_config.get('api_timeout', 30)
        self.retry_times = self.ts_config.get('retry_times', 3)
        self.retry_delay = self.ts_config.get('retry_delay', 1)
        self.rate_limit = self.ts_config.get('rate_limit', 200)  # 每分钟请求数
        
        # 频率控制
        self.last_call_time = 0
        self.call_count = 0
        self.call_times = []
        
        self.log_info(f"tushare API初始化成功，频率限制: {self.rate_limit}/分钟")
    
    def _rate_limit_check(self):
        """检查API调用频率限制"""
        current_time = time.time()
        
        # 清理1分钟前的调用记录
        self.call_times = [t for t in self.call_times if current_time - t < 60]
        
        # 检查是否超过频率限制
        if len(self.call_times) >= self.rate_limit:
            sleep_time = 60 - (current_time - self.call_times[0])
            if sleep_time > 0:
                self.log_warning(f"API调用频率限制，等待 {sleep_time:.1f} 秒")
                time.sleep(sleep_time)
        
        # 记录本次调用时间
        self.call_times.append(current_time)
    
    @retry(stop_max_attempt_number=3, wait_exponential_multiplier=1000, wait_exponential_max=10000)
    def _api_call(self, api_name: str, **kwargs) -> pd.DataFrame:
        """执行API调用，包含重试机制"""
        self._rate_limit_check()
        
        try:
            # 记录API调用
            self.log_api_call(f"调用API: {api_name}, 参数: {kwargs}")
            
            # 获取API方法
            api_method = getattr(self.pro, api_name)
            
            # 执行调用
            start_time = time.time()
            result = api_method(**kwargs)
            call_duration = time.time() - start_time
            
            if result is None or result.empty:
                self.log_warning(f"API {api_name} 返回空数据")
                return pd.DataFrame()
            
            self.log_api_call(f"API调用成功: {api_name}, 耗时: {call_duration:.2f}秒, 返回 {len(result)} 条记录")
            return result
            
        except Exception as e:
            self.log_error(f"API调用失败: {api_name}", e)
            raise
    
    # 基础数据API
    def get_stock_basic(self, **kwargs) -> pd.DataFrame:
        """获取股票列表"""
        return self._api_call('stock_basic', **kwargs)
    
    def get_trade_cal(self, **kwargs) -> pd.DataFrame:
        """获取交易日历"""
        return self._api_call('trade_cal', **kwargs)
    
    def get_stock_company(self, **kwargs) -> pd.DataFrame:
        """获取上市公司基本信息"""
        return self._api_call('stock_company', **kwargs)
    
    def get_hs_const(self, **kwargs) -> pd.DataFrame:
        """获取沪深股通成分股"""
        return self._api_call('hs_const', **kwargs)
    
    def get_concept(self, **kwargs) -> pd.DataFrame:
        """获取概念股分类"""
        return self._api_call('concept', **kwargs)
    
    def get_concept_detail(self, **kwargs) -> pd.DataFrame:
        """获取概念股列表"""
        return self._api_call('concept_detail', **kwargs)

    def get_namechange(self, **kwargs) -> pd.DataFrame:
        """获取股票曾用名"""
        return self._api_call('namechange', **kwargs)

    def get_new_share(self, **kwargs) -> pd.DataFrame:
        """获取新股列表"""
        return self._api_call('new_share', **kwargs)
    
    # 行情数据API
    def get_daily(self, **kwargs) -> pd.DataFrame:
        """获取日线行情"""
        return self._api_call('daily', **kwargs)
    
    def get_weekly(self, **kwargs) -> pd.DataFrame:
        """获取周线行情"""
        return self._api_call('weekly', **kwargs)
    
    def get_monthly(self, **kwargs) -> pd.DataFrame:
        """获取月线行情"""
        return self._api_call('monthly', **kwargs)
    
    def get_adj_factor(self, **kwargs) -> pd.DataFrame:
        """获取复权因子"""
        return self._api_call('adj_factor', **kwargs)
    
    def get_suspend_d(self, **kwargs) -> pd.DataFrame:
        """获取停牌信息"""
        return self._api_call('suspend_d', **kwargs)
    
    def get_daily_basic(self, **kwargs) -> pd.DataFrame:
        """获取每日指标"""
        return self._api_call('daily_basic', **kwargs)

    def get_moneyflow(self, **kwargs) -> pd.DataFrame:
        """获取资金流向"""
        return self._api_call('moneyflow', **kwargs)

    def get_stk_limit(self, **kwargs) -> pd.DataFrame:
        """获取涨跌停价格"""
        return self._api_call('stk_limit', **kwargs)
    
    # 财务数据API
    def get_income(self, **kwargs) -> pd.DataFrame:
        """获取利润表"""
        return self._api_call('income', **kwargs)
    
    def get_balancesheet(self, **kwargs) -> pd.DataFrame:
        """获取资产负债表"""
        return self._api_call('balancesheet', **kwargs)
    
    def get_cashflow(self, **kwargs) -> pd.DataFrame:
        """获取现金流量表"""
        return self._api_call('cashflow', **kwargs)
    
    def get_forecast(self, **kwargs) -> pd.DataFrame:
        """获取业绩预告"""
        return self._api_call('forecast', **kwargs)
    
    def get_express(self, **kwargs) -> pd.DataFrame:
        """获取业绩快报"""
        return self._api_call('express', **kwargs)
    
    def get_dividend(self, **kwargs) -> pd.DataFrame:
        """获取分红送股"""
        return self._api_call('dividend', **kwargs)
    
    def get_fina_indicator(self, **kwargs) -> pd.DataFrame:
        """获取财务指标"""
        return self._api_call('fina_indicator', **kwargs)

    def get_fina_audit(self, **kwargs) -> pd.DataFrame:
        """获取财务审计意见"""
        return self._api_call('fina_audit', **kwargs)

    def get_fina_mainbz(self, **kwargs) -> pd.DataFrame:
        """获取主营业务构成"""
        return self._api_call('fina_mainbz', **kwargs)
    
    def get_fina_audit(self, **kwargs) -> pd.DataFrame:
        """获取财务审计意见"""
        return self._api_call('fina_audit', **kwargs)
    
    def get_fina_mainbz(self, **kwargs) -> pd.DataFrame:
        """获取主营业务构成"""
        return self._api_call('fina_mainbz', **kwargs)
    
    # 指数数据API
    def get_index_basic(self, **kwargs) -> pd.DataFrame:
        """获取指数基本信息"""
        return self._api_call('index_basic', **kwargs)
    
    def get_index_daily(self, **kwargs) -> pd.DataFrame:
        """获取指数日线行情"""
        return self._api_call('index_daily', **kwargs)
    
    def get_index_weekly(self, **kwargs) -> pd.DataFrame:
        """获取指数周线行情"""
        return self._api_call('index_weekly', **kwargs)
    
    def get_index_monthly(self, **kwargs) -> pd.DataFrame:
        """获取指数月线行情"""
        return self._api_call('index_monthly', **kwargs)
    
    def get_index_weight(self, **kwargs) -> pd.DataFrame:
        """获取指数成分和权重"""
        return self._api_call('index_weight', **kwargs)
    
    def get_index_dailybasic(self, **kwargs) -> pd.DataFrame:
        """获取指数每日指标"""
        return self._api_call('index_dailybasic', **kwargs)
    
    # 市场参考数据API
    def get_moneyflow(self, **kwargs) -> pd.DataFrame:
        """获取资金流向"""
        return self._api_call('moneyflow', **kwargs)
    
    def get_stk_limit(self, **kwargs) -> pd.DataFrame:
        """获取涨跌停价格"""
        return self._api_call('stk_limit', **kwargs)
    
    def get_moneyflow_hsgt(self, **kwargs) -> pd.DataFrame:
        """获取沪深港通资金流向"""
        return self._api_call('moneyflow_hsgt', **kwargs)
    
    def get_hsgt_top10(self, **kwargs) -> pd.DataFrame:
        """获取沪深港通十大成交股"""
        return self._api_call('hsgt_top10', **kwargs)
    
    def get_ggt_top10(self, **kwargs) -> pd.DataFrame:
        """获取港股通十大成交股"""
        return self._api_call('ggt_top10', **kwargs)
    
    # 特色数据API
    def get_pledge_stat(self, **kwargs) -> pd.DataFrame:
        """获取股权质押统计数据"""
        return self._api_call('pledge_stat', **kwargs)
    
    def get_pledge_detail(self, **kwargs) -> pd.DataFrame:
        """获取股权质押明细"""
        return self._api_call('pledge_detail', **kwargs)
    
    def get_repurchase(self, **kwargs) -> pd.DataFrame:
        """获取股票回购"""
        return self._api_call('repurchase', **kwargs)
    
    def get_share_float(self, **kwargs) -> pd.DataFrame:
        """获取限售股解禁"""
        return self._api_call('share_float', **kwargs)
    
    def get_block_trade(self, **kwargs) -> pd.DataFrame:
        """获取大宗交易"""
        return self._api_call('block_trade', **kwargs)
    
    def get_stk_holdernumber(self, **kwargs) -> pd.DataFrame:
        """获取股东人数"""
        return self._api_call('stk_holdernumber', **kwargs)
    
    def get_stk_holdertrade(self, **kwargs) -> pd.DataFrame:
        """获取股东增减持"""
        return self._api_call('stk_holdertrade', **kwargs)
