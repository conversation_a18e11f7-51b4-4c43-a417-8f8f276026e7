<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>tushare数据查看器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .progress-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }
        
        .progress-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f0f0f0;
            border-radius: 8px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2196F3;
        }
        
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        
        .tables-section {
            padding: 30px;
        }
        
        .table-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
            margin-top: 20px;
        }

        .table-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px 16px;
            box-shadow: 0 1px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            min-height: 60px;
        }

        .table-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 3px 12px rgba(0,0,0,0.15);
        }

        .table-info-left {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1;
        }

        .table-name-group {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .table-name {
            font-size: 0.9em;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .table-chinese-name {
            font-size: 1.0em;
            color: #2196F3;
            margin: 0;
            font-weight: 600;
        }

        .table-description {
            color: #666;
            margin: 0;
            font-size: 0.8em;
            line-height: 1.3;
            max-width: 300px;
        }

        .table-info {
            color: #666;
            margin: 0;
            font-size: 0.8em;
        }

        .table-stats {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-count {
            font-size: 1.1em;
            font-weight: bold;
            color: #4CAF50;
            margin: 0;
        }

        .table-date {
            color: #999;
            font-size: 0.8em;
            margin: 0;
        }

        .tushare-link {
            display: inline-block;
            background: #FF9800;
            color: white;
            padding: 3px 8px;
            border-radius: 12px;
            text-decoration: none;
            font-size: 0.7em;
            margin-left: 8px;
            transition: background 0.2s ease;
            white-space: nowrap;
        }

        .tushare-link:hover {
            background: #F57C00;
            color: white;
            text-decoration: none;
        }

        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 8px;
            font-size: 0.7em;
            font-weight: bold;
            margin-left: 8px;
            white-space: nowrap;
        }

        .status-downloaded {
            background: #4CAF50;
            color: white;
        }

        .status-empty {
            background: #f44336;
            color: white;
        }

        .status-system {
            background: #9E9E9E;
            color: white;
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 0;
            border-radius: 10px;
            width: 90%;
            max-width: 1000px;
            max-height: 80vh;
            overflow: hidden;
        }
        
        .modal-header {
            background: #2196F3;
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .close {
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }
        
        .search-box {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-bottom: 15px;
            font-size: 16px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .pagination {
            text-align: center;
            margin-top: 20px;
        }
        
        .pagination button {
            background: #2196F3;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        
        .pagination button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .refresh-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-top: 15px;
        }
        
        .refresh-btn:hover {
            background: #45a049;
        }

        /* 新增：操作控制面板样式 */
        .control-section {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .control-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .control-card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-btn {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .control-btn:active {
            transform: translateY(0);
        }

        .control-btn.update-all {
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }

        .control-btn.update-all:hover {
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }

        .control-btn.download-missing {
            background: linear-gradient(45deg, #2196F3, #21CBF3);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }

        .control-btn.download-missing:hover {
            box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
        }

        .download-status {
            background: #f0f8ff;
            border: 1px solid #e3f2fd;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }

        .status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .status-header h3 {
            color: #1976d2;
            margin: 0;
        }

        .cancel-btn {
            background: #f44336;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .cancel-btn:hover {
            background: #d32f2f;
        }

        .status-progress {
            margin-bottom: 10px;
        }

        .status-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .status-fill {
            height: 100%;
            background: linear-gradient(45deg, #4CAF50, #8BC34A);
            border-radius: 10px;
            transition: width 0.3s ease;
        }

        #statusText {
            color: #666;
            font-size: 14px;
        }

        /* 新增：操作日志面板样式 */
        .logs-section {
            padding: 30px;
            background: #f8f9fa;
        }

        .logs-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .logs-card h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .logs-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .logs-btn {
            background: #607d8b;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }

        .logs-btn:hover {
            background: #546e7a;
        }

        .logs-container {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            padding: 15px;
            background: #fafafa;
        }

        .logs-placeholder {
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        .log-entry {
            background: white;
            border-left: 4px solid #2196F3;
            padding: 12px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .log-entry.success {
            border-left-color: #4CAF50;
        }

        .log-entry.failed {
            border-left-color: #f44336;
        }

        .log-entry.started {
            border-left-color: #ff9800;
        }

        .log-timestamp {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .log-operation {
            font-weight: bold;
            color: #333;
            margin-bottom: 3px;
        }

        .log-message {
            color: #666;
            font-size: 14px;
        }

        /* 新增：表卡片操作按钮样式 */
        .table-actions {
            margin-left: 15px;
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            align-items: center;
        }

        .action-btn {
            background: #f5f5f5;
            color: #333;
            border: 1px solid #ddd;
            padding: 4px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            transition: all 0.2s ease;
            white-space: nowrap;
            min-width: 60px;
        }

        .action-btn:hover {
            background: #e0e0e0;
            transform: translateY(-1px);
        }

        .action-btn.download-btn:hover {
            background: #e3f2fd;
            color: #1976d2;
            border-color: #1976d2;
        }

        .action-btn.update-btn:hover {
            background: #e8f5e8;
            color: #388e3c;
            border-color: #388e3c;
        }

        /* 移动端响应式样式 */
        @media (max-width: 768px) {
            .table-card {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                padding: 10px 12px;
            }

            .table-info-left {
                flex-direction: column;
                gap: 8px;
                width: 100%;
            }

            .table-stats {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
                width: 100%;
            }

            .table-actions {
                margin-left: 0;
                width: 100%;
                justify-content: flex-end;
            }

            .table-description {
                max-width: none;
            }

            .action-btn {
                min-width: 50px;
                font-size: 10px;
                padding: 3px 6px;
            }
        }

        /* 状态相关的新样式 */
        .status-badge.needs-update {
            background: #ff9800;
            color: white;
        }

        .status-badge.downloading {
            background: #2196F3;
            color: white;
            animation: pulse 1.5s infinite;
        }

        .status-badge.error {
            background: #f44336;
            color: white;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 tushare数据管理中心</h1>
            <p>实时查看下载进度 • 浏览所有数据表 • 对照官方文档</p>
        </div>
        
        <div class="progress-section">
            <div class="progress-card">
                <h2>📈 下载进度</h2>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 0%"></div>
                </div>
                <div id="progressText">加载中...</div>

                <div class="stats" id="progressStats">
                    <!-- 进度统计将在这里显示 -->
                </div>

                <button class="refresh-btn" onclick="loadProgress()">🔄 刷新进度</button>
            </div>
        </div>

        <!-- 新增：操作控制面板 -->
        <div class="control-section">
            <div class="control-card">
                <h2>🎛️ 数据管理操作</h2>
                <div class="control-buttons">
                    <button class="control-btn update-all" onclick="updateAllTables()">
                        🔄 一键更新所有数据
                    </button>
                    <button class="control-btn download-missing" onclick="downloadMissingTables()">
                        📥 下载所有缺失数据
                    </button>
                </div>

                <!-- 实时下载状态显示 -->
                <div id="downloadStatus" class="download-status" style="display: none;">
                    <div class="status-header">
                        <h3 id="statusTitle">下载状态</h3>
                        <button class="cancel-btn" onclick="cancelCurrentDownload()" id="cancelBtn" style="display: none;">❌ 取消</button>
                    </div>
                    <div class="status-progress">
                        <div class="status-bar">
                            <div class="status-fill" id="statusFill" style="width: 0%"></div>
                        </div>
                        <div id="statusText">准备中...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 新增：操作日志面板 -->
        <div class="logs-section">
            <div class="logs-card">
                <h2>📝 操作日志</h2>
                <div class="logs-controls">
                    <button class="logs-btn" onclick="loadLogs()">🔄 刷新日志</button>
                    <button class="logs-btn" onclick="clearLogsDisplay()">🗑️ 清空显示</button>
                </div>
                <div id="operationLogs" class="logs-container">
                    <div class="logs-placeholder">暂无操作日志</div>
                </div>
            </div>
        </div>
        
        <div class="tables-section">
            <h2>📋 tushare数据表概览</h2>
            <p style="color: #666; margin-bottom: 15px;">
                点击数据表卡片查看详细数据 • 点击"📖 查看tushare文档"了解数据结构
            </p>
            <button class="refresh-btn" onclick="loadTables()">🔄 刷新数据</button>

            <div class="table-grid" id="tableGrid">
                <div class="loading">正在加载数据表信息...</div>
            </div>
        </div>
    </div>
    
    <!-- 数据查看模态框 -->
    <div id="dataModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="modalTitle">数据表</h2>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            <div class="modal-body">
                <input type="text" id="searchInput" class="search-box" placeholder="搜索数据..." onkeyup="searchData()">
                <div id="modalContent">
                    <div class="loading">正在加载数据...</div>
                </div>
                <div class="pagination" id="pagination"></div>
            </div>
        </div>
    </div>

    <script>
        let currentTable = '';
        let currentPage = 1;
        let searchTerm = '';

        // 页面加载时初始化
        window.onload = function() {
            loadProgress();
            loadTables();
            
            // 每30秒自动刷新进度
            setInterval(loadProgress, 30000);
        };

        // 加载进度信息
        function loadProgress() {
            fetch('/api/progress')
                .then(response => response.json())
                .then(data => {
                    const progressFill = document.getElementById('progressFill');
                    const progressText = document.getElementById('progressText');
                    const progressStats = document.getElementById('progressStats');
                    
                    progressFill.style.width = data.progress + '%';
                    progressText.textContent = `进度: ${data.progress}% (${data.completed}/${data.total})`;
                    
                    // 显示详细统计
                    let statsHtml = `
                        <div class="stat-item">
                            <div class="stat-number">${data.total}</div>
                            <div class="stat-label">总任务数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.completed}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${data.progress}%</div>
                            <div class="stat-label">完成率</div>
                        </div>
                    `;
                    
                    if (data.status_counts) {
                        for (let [status, count] of Object.entries(data.status_counts)) {
                            if (status !== 'completed') {
                                statsHtml += `
                                    <div class="stat-item">
                                        <div class="stat-number">${count}</div>
                                        <div class="stat-label">${status}</div>
                                    </div>
                                `;
                            }
                        }
                    }
                    
                    progressStats.innerHTML = statsHtml;
                })
                .catch(error => {
                    console.error('加载进度失败:', error);
                    document.getElementById('progressText').textContent = '加载进度失败';
                });
        }

        // 加载数据表信息
        function loadTables() {
            fetch('/api/tables/enhanced')
                .then(response => response.json())
                .then(data => {
                    const tableGrid = document.getElementById('tableGrid');
                    
                    if (data.length === 0) {
                        tableGrid.innerHTML = '<div class="loading">暂无数据表</div>';
                        return;
                    }
                    
                    let html = '';
                    data.forEach(table => {
                        const countDisplay = table.count ? table.count.toLocaleString() : '0';
                        const dateDisplay = table.latest_date ? `最新数据: ${table.latest_date}` : '无日期信息';

                        // 使用新的状态信息
                        const statusBadge = table.status_text || '未知';
                        const statusClass = 'status-' + (table.status_class || 'unknown');

                        // tushare链接
                        const tushareLink = table.tushare_url ?
                            `<a href="${table.tushare_url}" target="_blank" class="tushare-link" onclick="event.stopPropagation();">📖 查看tushare文档</a>` :
                            '';

                        // 生成操作按钮
                        const actionButtons = `
                            <div class="table-actions" onclick="event.stopPropagation();">
                                <button class="action-btn download-btn" onclick="showDateModal('${table.name}', 'full')" title="重新下载">
                                    📥 重新下载
                                </button>
                                <button class="action-btn update-btn" onclick="showDateModal('${table.name}', 'incremental')" title="增量更新">
                                    🔄 增量更新
                                </button>
                            </div>
                        `;

                        html += `
                            <div class="table-card" onclick="openTable('${table.name}')">
                                <div class="table-info-left">
                                    <div class="table-name-group">
                                        <div class="table-chinese-name">${table.chinese_name}</div>
                                        <div class="table-name">${table.name}</div>
                                    </div>
                                    <div class="table-description">${table.description}</div>
                                    <span class="status-badge ${statusClass}">${statusBadge}</span>
                                    ${tushareLink}
                                </div>
                                <div class="table-stats">
                                    <div class="table-count">${countDisplay} 条</div>
                                    <div class="table-info">列数: ${table.columns.length}</div>
                                    <div class="table-date">${dateDisplay}</div>
                                </div>
                                ${actionButtons}
                            </div>
                        `;
                    });
                    
                    tableGrid.innerHTML = html;
                })
                .catch(error => {
                    console.error('加载数据表失败:', error);
                    document.getElementById('tableGrid').innerHTML = '<div class="loading">加载失败</div>';
                });
        }

        // 打开数据表
        function openTable(tableName) {
            currentTable = tableName;
            currentPage = 1;
            searchTerm = '';
            
            document.getElementById('modalTitle').textContent = `📊 ${tableName}`;
            document.getElementById('searchInput').value = '';
            document.getElementById('dataModal').style.display = 'block';
            
            loadTableData();
        }

        // 加载表数据
        function loadTableData() {
            const url = searchTerm ? 
                `/api/search/${currentTable}?q=${encodeURIComponent(searchTerm)}&page=${currentPage}` :
                `/api/table/${currentTable}?page=${currentPage}`;
            
            document.getElementById('modalContent').innerHTML = '<div class="loading">正在加载数据...</div>';
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        document.getElementById('modalContent').innerHTML = `<div class="loading">错误: ${data.error}</div>`;
                        return;
                    }
                    
                    if (data.data.length === 0) {
                        document.getElementById('modalContent').innerHTML = '<div class="loading">暂无数据</div>';
                        return;
                    }
                    
                    // 生成表格
                    let html = '<table class="data-table"><thead><tr>';
                    const columns = Object.keys(data.data[0]);
                    columns.forEach(col => {
                        html += `<th>${col}</th>`;
                    });
                    html += '</tr></thead><tbody>';
                    
                    data.data.forEach(row => {
                        html += '<tr>';
                        columns.forEach(col => {
                            html += `<td>${row[col] || ''}</td>`;
                        });
                        html += '</tr>';
                    });
                    html += '</tbody></table>';
                    
                    document.getElementById('modalContent').innerHTML = html;
                    
                    // 生成分页
                    generatePagination(data);
                })
                .catch(error => {
                    console.error('加载表数据失败:', error);
                    document.getElementById('modalContent').innerHTML = '<div class="loading">加载失败</div>';
                });
        }

        // 生成分页
        function generatePagination(data) {
            const pagination = document.getElementById('pagination');
            let html = '';
            
            // 上一页
            html += `<button ${currentPage <= 1 ? 'disabled' : ''} onclick="changePage(${currentPage - 1})">上一页</button>`;
            
            // 页码信息
            html += `<span style="margin: 0 15px;">第 ${currentPage} 页，共 ${data.total_pages} 页 (总计 ${data.total} 条记录)</span>`;
            
            // 下一页
            html += `<button ${currentPage >= data.total_pages ? 'disabled' : ''} onclick="changePage(${currentPage + 1})">下一页</button>`;
            
            pagination.innerHTML = html;
        }

        // 切换页面
        function changePage(page) {
            currentPage = page;
            loadTableData();
        }

        // 搜索数据
        function searchData() {
            searchTerm = document.getElementById('searchInput').value;
            currentPage = 1;
            loadTableData();
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('dataModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('dataModal');
            if (event.target == modal) {
                modal.style.display = 'none';
            }
        }

        // ==================== 新增下载管理功能 ====================

        let currentTaskId = null;
        let statusCheckInterval = null;

        // 原有的下载函数已移动到页面底部，支持日期参数

        // 批量更新所有数据
        function updateAllTables() {
            if (!confirm('确定要更新所有已下载的数据表吗？\n\n这可能需要较长时间，请耐心等待。')) {
                return;
            }

            fetch('/api/download/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation: 'update_all'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }

        // 下载所有缺失数据
        function downloadMissingTables() {
            if (!confirm('确定要下载所有缺失的数据表吗？\n\n这可能需要很长时间，请确保网络连接稳定。')) {
                return;
            }

            fetch('/api/download/batch', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    operation: 'download_missing'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }

        // 显示下载状态
        function showDownloadStatus(message) {
            const statusDiv = document.getElementById('downloadStatus');
            const statusText = document.getElementById('statusText');
            const statusFill = document.getElementById('statusFill');
            const cancelBtn = document.getElementById('cancelBtn');

            statusDiv.style.display = 'block';
            statusText.textContent = message;
            statusFill.style.width = '0%';
            cancelBtn.style.display = 'inline-block';
        }

        // 隐藏下载状态
        function hideDownloadStatus() {
            const statusDiv = document.getElementById('downloadStatus');
            statusDiv.style.display = 'none';
            currentTaskId = null;

            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }

        // 开始状态监控
        function startStatusMonitoring() {
            if (statusCheckInterval) {
                clearInterval(statusCheckInterval);
            }

            statusCheckInterval = setInterval(checkDownloadStatus, 2000); // 每2秒检查一次
        }

        // 检查下载状态
        function checkDownloadStatus() {
            if (!currentTaskId) {
                return;
            }

            fetch(`/api/download/status?task_id=${currentTaskId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.status) {
                        const status = data.status;
                        const statusText = document.getElementById('statusText');
                        const statusFill = document.getElementById('statusFill');

                        statusText.textContent = status.message;
                        statusFill.style.width = status.progress + '%';

                        if (status.status === 'completed') {
                            setTimeout(() => {
                                hideDownloadStatus();
                                loadProgress(); // 刷新进度
                                loadTables(); // 刷新表列表
                                loadLogs(); // 刷新日志
                                alert('操作完成！');
                            }, 2000);
                        } else if (status.status === 'failed') {
                            setTimeout(() => {
                                hideDownloadStatus();
                                alert('操作失败: ' + status.message);
                            }, 2000);
                        } else if (status.status === 'cancelled') {
                            hideDownloadStatus();
                            alert('操作已取消');
                        }
                    }
                })
                .catch(error => {
                    console.error('Status check error:', error);
                });
        }

        // 取消当前下载
        function cancelCurrentDownload() {
            if (!currentTaskId) {
                return;
            }

            if (!confirm('确定要取消当前操作吗？')) {
                return;
            }

            fetch('/api/download/cancel', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    task_id: currentTaskId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    hideDownloadStatus();
                    alert('操作已取消');
                } else {
                    alert('取消失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Cancel error:', error);
                alert('取消失败: ' + error.message);
            });
        }

        // 加载操作日志
        function loadLogs() {
            fetch('/api/download/logs?limit=20')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayLogs(data.logs);
                    } else {
                        console.error('Failed to load logs:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading logs:', error);
                });
        }

        // 显示日志
        function displayLogs(logs) {
            const logsContainer = document.getElementById('operationLogs');

            if (!logs || logs.length === 0) {
                logsContainer.innerHTML = '<div class="logs-placeholder">暂无操作日志</div>';
                return;
            }

            let html = '';
            logs.reverse().forEach(log => {
                const statusClass = log.status === 'completed' ? 'success' :
                                  log.status === 'failed' ? 'failed' :
                                  log.status === 'started' ? 'started' : '';

                html += `
                    <div class="log-entry ${statusClass}">
                        <div class="log-timestamp">${log.timestamp}</div>
                        <div class="log-operation">${log.operation} - ${log.chinese_name}</div>
                        <div class="log-message">${log.message}</div>
                    </div>
                `;
            });

            logsContainer.innerHTML = html;
        }

        // 清空日志显示
        function clearLogsDisplay() {
            const logsContainer = document.getElementById('operationLogs');
            logsContainer.innerHTML = '<div class="logs-placeholder">日志显示已清空</div>';
        }

        // 页面加载时初始化日志
        document.addEventListener('DOMContentLoaded', function() {
            loadLogs();

            // 每分钟自动刷新日志
            setInterval(loadLogs, 60000);
        });

        // 日期选择模态框相关函数
        let currentTableName = '';
        let currentMode = '';

        function showDateModal(tableName, mode) {
            currentTableName = tableName;
            currentMode = mode;

            // 设置默认日期
            const today = new Date();
            const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
            document.getElementById('endDate').value = today.toISOString().split('T')[0];

            document.getElementById('dateModal').style.display = 'block';
        }

        function closeDateModal() {
            document.getElementById('dateModal').style.display = 'none';
        }

        function downloadWithDates() {
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;

            downloadSingleTable(currentTableName, currentMode, startDate, endDate);
            closeDateModal();
        }

        function downloadWithoutDates() {
            downloadSingleTable(currentTableName, currentMode);
            closeDateModal();
        }

        // 修改原有的下载函数
        function downloadSingleTable(tableName, mode, startDate = null, endDate = null) {
            const modeText = mode === 'full' ? '重新下载' : '增量更新';
            let confirmMessage = `确定要${modeText} "${tableName}" 表吗？\n\n${mode === 'full' ? '这将替换现有的所有数据。' : '这将只下载最新的数据。'}`;

            if (startDate && endDate) {
                confirmMessage += `\n\n日期范围: ${startDate} 到 ${endDate}`;
            } else if (startDate) {
                confirmMessage += `\n\n开始日期: ${startDate}`;
            } else if (endDate) {
                confirmMessage += `\n\n结束日期: ${endDate}`;
            }

            if (!confirm(confirmMessage)) {
                return;
            }

            const data = {
                table_name: tableName,
                mode: mode
            };

            if (startDate) data.start_date = startDate;
            if (endDate) data.end_date = endDate;

            fetch('/api/download/single', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    showDownloadStatus(data.message);
                    startStatusMonitoring();
                } else {
                    alert('操作失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('操作失败: ' + error.message);
            });
        }
    </script>

    <!-- 日期选择模态框 -->
    <div id="dateModal" class="modal">
        <div class="modal-content" style="max-width: 500px; margin: 10% auto;">
            <div class="modal-header">
                <h3>选择下载日期范围</h3>
                <span class="close" onclick="closeDateModal()">&times;</span>
            </div>
            <div class="modal-body" style="padding: 20px;">
                <div style="margin-bottom: 15px;">
                    <label for="startDate" style="display: block; margin-bottom: 5px; font-weight: bold;">开始日期:</label>
                    <input type="date" id="startDate" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="margin-bottom: 20px;">
                    <label for="endDate" style="display: block; margin-bottom: 5px; font-weight: bold;">结束日期:</label>
                    <input type="date" id="endDate" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                </div>
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button onclick="downloadWithoutDates()" style="padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        不限日期下载
                    </button>
                    <button onclick="downloadWithDates()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        按日期下载
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
