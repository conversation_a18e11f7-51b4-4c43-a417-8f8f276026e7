#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
tushare数据网页查看器
提供简单易用的网页界面查看所有下载的数据
"""

from flask import Flask, render_template, request, jsonify
import sqlite3
import pandas as pd
import json
from datetime import datetime, timedelta
import os

app = Flask(__name__)

# 数据库路径
DB_PATH = 'data/tushare_data.db'

def get_db_connection():
    """获取数据库连接"""
    if not os.path.exists(DB_PATH):
        return None
    return sqlite3.connect(DB_PATH)

def get_all_tables():
    """获取所有数据表"""
    conn = get_db_connection()
    if not conn:
        return []
    
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
    tables = [row[0] for row in cursor.fetchall()]
    conn.close()
    return tables

def get_table_info(table_name):
    """获取表信息"""
    conn = get_db_connection()
    if not conn:
        return None
    
    try:
        cursor = conn.cursor()
        
        # 获取记录数
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = [row[1] for row in cursor.fetchall()]
        
        # 获取最新更新时间（如果有时间字段）
        last_update = None
        time_columns = ['trade_date', 'cal_date', 'end_date', 'ann_date', 'created_at', 'updated_at']
        for col in time_columns:
            if col in columns:
                try:
                    cursor.execute(f"SELECT MAX({col}) FROM {table_name}")
                    result = cursor.fetchone()[0]
                    if result:
                        last_update = result
                        break
                except:
                    continue
        
        conn.close()
        return {
            'count': count,
            'columns': columns,
            'last_update': last_update
        }
    except Exception as e:
        conn.close()
        return {'error': str(e)}

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/api/tables')
def api_tables():
    """获取所有表的信息"""
    tables = get_all_tables()
    table_info = {}
    
    for table in tables:
        info = get_table_info(table)
        if info:
            table_info[table] = info
    
    return jsonify(table_info)

@app.route('/api/table/<table_name>')
def api_table_data(table_name):
    """获取表数据"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': '数据库连接失败'})
    
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 50))
        search = request.args.get('search', '')
        
        offset = (page - 1) * per_page
        
        # 构建查询
        if search:
            # 简单搜索：在所有文本列中搜索
            query = f"SELECT * FROM {table_name} WHERE CAST(rowid AS TEXT) LIKE '%{search}%'"
            count_query = f"SELECT COUNT(*) FROM {table_name} WHERE CAST(rowid AS TEXT) LIKE '%{search}%'"
        else:
            query = f"SELECT * FROM {table_name}"
            count_query = f"SELECT COUNT(*) FROM {table_name}"
        
        # 添加排序和分页
        query += f" ORDER BY rowid DESC LIMIT {per_page} OFFSET {offset}"
        
        # 获取数据
        df = pd.read_sql_query(query, conn)
        
        # 获取总数
        cursor = conn.cursor()
        cursor.execute(count_query)
        total = cursor.fetchone()[0]
        
        conn.close()
        
        # 转换为JSON格式
        data = df.to_dict('records')
        
        return jsonify({
            'data': data,
            'total': total,
            'page': page,
            'per_page': per_page,
            'total_pages': (total + per_page - 1) // per_page
        })
        
    except Exception as e:
        conn.close()
        return jsonify({'error': str(e)})

@app.route('/api/stock_list')
def api_stock_list():
    """获取股票列表"""
    conn = get_db_connection()
    if not conn:
        return jsonify([])
    
    try:
        cursor = conn.cursor()
        cursor.execute("SELECT ts_code, symbol, name FROM stock_basic ORDER BY ts_code")
        stocks = [{'ts_code': row[0], 'symbol': row[1], 'name': row[2]} for row in cursor.fetchall()]
        conn.close()
        return jsonify(stocks)
    except:
        conn.close()
        return jsonify([])

@app.route('/api/stock_data/<ts_code>')
def api_stock_data(ts_code):
    """获取单只股票的行情数据"""
    conn = get_db_connection()
    if not conn:
        return jsonify([])
    
    try:
        days = int(request.args.get('days', 30))
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days)).strftime('%Y%m%d')
        
        query = """
        SELECT trade_date, open, high, low, close, vol, amount 
        FROM daily 
        WHERE ts_code = ? AND trade_date >= ? AND trade_date <= ?
        ORDER BY trade_date DESC
        """
        
        df = pd.read_sql_query(query, conn, params=[ts_code, start_date, end_date])
        conn.close()
        
        return jsonify(df.to_dict('records'))
    except Exception as e:
        conn.close()
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    # 创建模板目录
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    print("🌐 启动tushare数据查看器...")
    print("📊 访问地址: http://localhost:5000")
    print("🔍 你可以在网页上查看所有下载的数据!")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
