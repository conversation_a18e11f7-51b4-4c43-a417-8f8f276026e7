#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
支持SQLite和PostgreSQL，提供数据表创建、管理等功能
"""

import os
import sqlite3
from pathlib import Path
from typing import Optional, Dict, Any
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String, DateTime, Float, Text, Boolean
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
from loguru import logger

Base = declarative_base()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config):
        """初始化数据库管理器"""
        self.config = config
        self.db_config = config.get('database', {})
        self.db_type = self.db_config.get('type', 'sqlite')
        self.engine = None
        self.Session = None
        self.metadata = MetaData()
        
    def initialize(self):
        """初始化数据库连接和表结构"""
        try:
            # 创建数据库引擎
            self.engine = self._create_engine()
            
            # 创建会话工厂
            self.Session = sessionmaker(bind=self.engine)
            
            # 创建表结构
            self._create_tables()
            
            logger.info(f"数据库初始化成功，类型: {self.db_type}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_engine(self):
        """创建数据库引擎"""
        if self.db_type == 'sqlite':
            db_path = self.db_config.get('sqlite', {}).get('path', 'data/tushare_data.db')
            # 确保数据目录存在
            Path(db_path).parent.mkdir(parents=True, exist_ok=True)
            connection_string = f"sqlite:///{db_path}"
            
        elif self.db_type == 'postgresql':
            pg_config = self.db_config.get('postgresql', {})
            host = pg_config.get('host', 'localhost')
            port = pg_config.get('port', 5432)
            database = pg_config.get('database', 'tushare')
            username = pg_config.get('username', 'postgres')
            password = pg_config.get('password', 'password')
            connection_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"
            
        else:
            raise ValueError(f"不支持的数据库类型: {self.db_type}")
        
        return create_engine(connection_string, echo=False)
    
    def _create_tables(self):
        """创建数据表"""
        # 进度跟踪表
        progress_table = Table(
            'download_progress', self.metadata,
            Column('id', Integer, primary_key=True),
            Column('data_type', String(50), nullable=False),
            Column('table_name', String(100), nullable=False),
            Column('start_date', String(8)),
            Column('end_date', String(8)),
            Column('current_date', String(8)),
            Column('total_records', Integer, default=0),
            Column('downloaded_records', Integer, default=0),
            Column('status', String(20), default='pending'),  # pending, running, completed, failed
            Column('error_message', Text),
            Column('created_at', DateTime),
            Column('updated_at', DateTime),
            Column('completed_at', DateTime),
        )
        
        # 数据状态表
        data_status_table = Table(
            'data_status', self.metadata,
            Column('id', Integer, primary_key=True),
            Column('table_name', String(100), nullable=False),
            Column('date', String(8)),
            Column('record_count', Integer, default=0),
            Column('is_complete', Boolean, default=False),
            Column('last_updated', DateTime),
            Column('checksum', String(64)),  # 数据校验和
        )
        
        # 错误日志表
        error_log_table = Table(
            'error_logs', self.metadata,
            Column('id', Integer, primary_key=True),
            Column('data_type', String(50)),
            Column('table_name', String(100)),
            Column('error_type', String(50)),
            Column('error_message', Text),
            Column('retry_count', Integer, default=0),
            Column('created_at', DateTime),
            Column('resolved_at', DateTime),
        )
        
        # 创建所有表
        self.metadata.create_all(self.engine)
        
        # 创建tushare数据表
        self._create_tushare_tables()
    
    def _create_tushare_tables(self):
        """创建tushare数据表"""
        # 这里定义所有tushare数据表的结构
        # 基础数据表
        self._create_basic_tables()
        # 行情数据表
        self._create_market_tables()
        # 财务数据表
        self._create_financial_tables()
        # 指数数据表
        self._create_index_tables()
    
    def _create_basic_tables(self):
        """创建基础数据表"""
        # 股票列表
        stock_basic = Table(
            'stock_basic', self.metadata,
            Column('ts_code', String(20), primary_key=True),
            Column('symbol', String(10)),
            Column('name', String(20)),
            Column('area', String(20)),
            Column('industry', String(50)),
            Column('market', String(20)),
            Column('list_date', String(8)),
            Column('is_hs', String(1)),
        )
        
        # 交易日历
        trade_cal = Table(
            'trade_cal', self.metadata,
            Column('exchange', String(10)),
            Column('cal_date', String(8), primary_key=True),
            Column('is_open', Integer),
            Column('pretrade_date', String(8)),
        )
        
        self.metadata.create_all(self.engine)
    
    def _create_market_tables(self):
        """创建行情数据表"""
        # 日线行情
        daily = Table(
            'daily', self.metadata,
            Column('ts_code', String(20)),
            Column('trade_date', String(8)),
            Column('open', Float),
            Column('high', Float),
            Column('low', Float),
            Column('close', Float),
            Column('pre_close', Float),
            Column('change', Float),
            Column('pct_chg', Float),
            Column('vol', Float),
            Column('amount', Float),
        )
        
        self.metadata.create_all(self.engine)
    
    def _create_financial_tables(self):
        """创建财务数据表"""
        # 利润表
        income = Table(
            'income', self.metadata,
            Column('ts_code', String(20)),
            Column('ann_date', String(8)),
            Column('f_ann_date', String(8)),
            Column('end_date', String(8)),
            Column('report_type', String(20)),
            Column('comp_type', String(20)),
            Column('total_revenue', Float),
            Column('revenue', Float),
            Column('int_income', Float),
            Column('prem_earned', Float),
            Column('comm_income', Float),
            Column('n_commis_income', Float),
            Column('n_oth_income', Float),
            Column('n_oth_b_income', Float),
            Column('prem_income', Float),
            Column('out_prem', Float),
            Column('une_prem_reser', Float),
            Column('reins_income', Float),
            Column('n_sec_tb_income', Float),
            Column('n_sec_uw_income', Float),
            Column('n_asset_mg_income', Float),
            Column('oth_b_income', Float),
            Column('fv_value_chg_gain', Float),
            Column('invest_income', Float),
            Column('ass_invest_income', Float),
            Column('forex_gain', Float),
            Column('total_cogs', Float),
            Column('oper_cost', Float),
            Column('int_exp', Float),
            Column('comm_exp', Float),
            Column('biz_tax_surchg', Float),
            Column('sell_exp', Float),
            Column('admin_exp', Float),
            Column('fin_exp', Float),
            Column('assets_impair_loss', Float),
            Column('prem_refund', Float),
            Column('compens_payout', Float),
            Column('reser_insur_liab', Float),
            Column('div_payt', Float),
            Column('reins_exp', Float),
            Column('oper_exp', Float),
            Column('compens_payout_refu', Float),
            Column('insur_reser_refu', Float),
            Column('reins_cost_refund', Float),
            Column('other_bus_cost', Float),
            Column('operate_profit', Float),
            Column('non_oper_income', Float),
            Column('non_oper_exp', Float),
            Column('nca_disploss', Float),
            Column('total_profit', Float),
            Column('income_tax', Float),
            Column('n_income', Float),
            Column('n_income_attr_p', Float),
            Column('minority_gain', Float),
            Column('oth_compr_income', Float),
            Column('t_compr_income', Float),
            Column('compr_inc_attr_p', Float),
            Column('compr_inc_attr_m_s', Float),
            Column('ebit', Float),
            Column('ebitda', Float),
            Column('insurance_exp', Float),
            Column('undist_profit', Float),
            Column('distable_profit', Float),
            Column('update_flag', String(1)),
        )
        
        self.metadata.create_all(self.engine)
    
    def _create_index_tables(self):
        """创建指数数据表"""
        # 指数基本信息
        index_basic = Table(
            'index_basic', self.metadata,
            Column('ts_code', String(20), primary_key=True),
            Column('name', String(100)),
            Column('fullname', String(200)),
            Column('market', String(20)),
            Column('publisher', String(100)),
            Column('index_type', String(20)),
            Column('category', String(20)),
            Column('base_date', String(8)),
            Column('base_point', Float),
            Column('list_date', String(8)),
            Column('weight_rule', String(200)),
            Column('desc', Text),
            Column('exp_date', String(8)),
        )
        
        # 指数日线行情
        index_daily = Table(
            'index_daily', self.metadata,
            Column('ts_code', String(20)),
            Column('trade_date', String(8)),
            Column('close', Float),
            Column('open', Float),
            Column('high', Float),
            Column('low', Float),
            Column('pre_close', Float),
            Column('change', Float),
            Column('pct_chg', Float),
            Column('vol', Float),
            Column('amount', Float),
        )
        
        self.metadata.create_all(self.engine)
    
    def get_session(self):
        """获取数据库会话"""
        return self.Session()
    
    def execute_sql(self, sql: str, params: Optional[Dict[str, Any]] = None):
        """执行SQL语句"""
        with self.engine.connect() as conn:
            return conn.execute(sql, params or {})
    
    def close(self):
        """关闭数据库连接"""
        if self.engine:
            self.engine.dispose()
            logger.info("数据库连接已关闭")
