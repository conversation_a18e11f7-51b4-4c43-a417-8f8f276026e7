#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
性能优化模块
优化下载性能，提供并发控制、缓存机制等功能
"""

import asyncio
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable
from dataclasses import dataclass
from src.core.logger import LoggerMixin


@dataclass
class PerformanceConfig:
    """性能配置"""
    max_workers: int = 4  # 最大并发数
    batch_size: int = 100  # 批处理大小
    retry_attempts: int = 3  # 重试次数
    retry_delay: float = 1.0  # 重试延迟(秒)
    rate_limit_calls: int = 200  # 速率限制(每分钟调用数)
    rate_limit_window: int = 60  # 速率限制窗口(秒)
    cache_enabled: bool = True  # 是否启用缓存
    cache_ttl: int = 3600  # 缓存TTL(秒)


class RateLimiter:
    """速率限制器"""
    
    def __init__(self, calls_per_window: int, window_seconds: int):
        self.calls_per_window = calls_per_window
        self.window_seconds = window_seconds
        self.calls = []
        self.lock = threading.Lock()
    
    def acquire(self) -> bool:
        """获取调用许可"""
        with self.lock:
            now = time.time()
            
            # 清理过期的调用记录
            self.calls = [call_time for call_time in self.calls 
                         if now - call_time < self.window_seconds]
            
            # 检查是否超过限制
            if len(self.calls) >= self.calls_per_window:
                return False
            
            # 记录本次调用
            self.calls.append(now)
            return True
    
    def wait_if_needed(self):
        """如果需要则等待"""
        while not self.acquire():
            time.sleep(0.1)


class SimpleCache:
    """简单缓存实现"""
    
    def __init__(self, ttl: int = 3600):
        self.ttl = ttl
        self.cache = {}
        self.timestamps = {}
        self.lock = threading.Lock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # 检查是否过期
            if time.time() - self.timestamps[key] > self.ttl:
                del self.cache[key]
                del self.timestamps[key]
                return None
            
            return self.cache[key]
    
    def set(self, key: str, value: Any):
        """设置缓存值"""
        with self.lock:
            self.cache[key] = value
            self.timestamps[key] = time.time()
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.timestamps.clear()


class PerformanceOptimizer(LoggerMixin):
    """性能优化器"""
    
    def __init__(self, config: PerformanceConfig = None):
        super().__init__()
        self.config = config or PerformanceConfig()
        
        # 初始化组件
        self.rate_limiter = RateLimiter(
            self.config.rate_limit_calls,
            self.config.rate_limit_window
        )
        
        self.cache = SimpleCache(self.config.cache_ttl) if self.config.cache_enabled else None
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0.0,
            'start_time': time.time()
        }
    
    def execute_with_optimization(self, 
                                func: Callable,
                                tasks: List[Dict[str, Any]],
                                use_cache: bool = True) -> List[Any]:
        """使用优化策略执行任务"""
        self.log_info(f"开始执行 {len(tasks)} 个任务，使用 {self.config.max_workers} 个并发")
        
        start_time = time.time()
        results = []
        
        # 分批处理
        batches = self._create_batches(tasks, self.config.batch_size)
        
        for i, batch in enumerate(batches):
            self.log_debug(f"处理第 {i+1}/{len(batches)} 批，包含 {len(batch)} 个任务")
            
            batch_results = self._execute_batch(func, batch, use_cache)
            results.extend(batch_results)
            
            # 批次间休息，避免过载
            if i < len(batches) - 1:
                time.sleep(0.1)
        
        end_time = time.time()
        self.stats['total_time'] += (end_time - start_time)
        
        self.log_info(f"任务执行完成，耗时 {end_time - start_time:.2f} 秒")
        self._log_performance_stats()
        
        return results
    
    def execute_with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """带重试机制的执行"""
        last_exception = None
        
        for attempt in range(self.config.retry_attempts):
            try:
                # 速率限制
                self.rate_limiter.wait_if_needed()
                
                # 执行函数
                start_time = time.time()
                result = func(*args, **kwargs)
                
                # 更新统计
                self.stats['total_requests'] += 1
                self.stats['successful_requests'] += 1
                
                return result
                
            except Exception as e:
                last_exception = e
                self.stats['total_requests'] += 1
                self.stats['failed_requests'] += 1
                
                if attempt < self.config.retry_attempts - 1:
                    delay = self.config.retry_delay * (2 ** attempt)  # 指数退避
                    self.log_warning(f"执行失败，{delay:.1f}秒后重试 (第{attempt+1}次): {e}")
                    time.sleep(delay)
                else:
                    self.log_error(f"执行失败，已达最大重试次数: {e}")
        
        raise last_exception
    
    def get_cached_or_execute(self, cache_key: str, func: Callable, *args, **kwargs) -> Any:
        """获取缓存或执行函数"""
        if not self.cache:
            return self.execute_with_retry(func, *args, **kwargs)
        
        # 尝试从缓存获取
        cached_result = self.cache.get(cache_key)
        if cached_result is not None:
            self.stats['cache_hits'] += 1
            self.log_debug(f"缓存命中: {cache_key}")
            return cached_result
        
        # 缓存未命中，执行函数
        self.stats['cache_misses'] += 1
        result = self.execute_with_retry(func, *args, **kwargs)
        
        # 存入缓存
        self.cache.set(cache_key, result)
        self.log_debug(f"结果已缓存: {cache_key}")
        
        return result
    
    def _execute_batch(self, func: Callable, batch: List[Dict[str, Any]], use_cache: bool) -> List[Any]:
        """执行批次任务"""
        results = []
        
        with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
            # 提交任务
            future_to_task = {}
            for task in batch:
                if use_cache and self.cache:
                    cache_key = self._generate_cache_key(task)
                    future = executor.submit(self.get_cached_or_execute, cache_key, func, **task)
                else:
                    future = executor.submit(self.execute_with_retry, func, **task)
                future_to_task[future] = task
            
            # 收集结果
            for future in as_completed(future_to_task):
                task = future_to_task[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    self.log_error(f"任务执行失败: {task}, 错误: {e}")
                    results.append(None)
        
        return results
    
    def _create_batches(self, items: List[Any], batch_size: int) -> List[List[Any]]:
        """创建批次"""
        batches = []
        for i in range(0, len(items), batch_size):
            batches.append(items[i:i + batch_size])
        return batches
    
    def _generate_cache_key(self, task: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 简单的缓存键生成策略
        import hashlib
        task_str = str(sorted(task.items()))
        return hashlib.md5(task_str.encode()).hexdigest()
    
    def _log_performance_stats(self):
        """记录性能统计"""
        total_requests = self.stats['total_requests']
        if total_requests == 0:
            return
        
        success_rate = self.stats['successful_requests'] / total_requests * 100
        
        self.log_info(f"性能统计:")
        self.log_info(f"  总请求数: {total_requests}")
        self.log_info(f"  成功率: {success_rate:.1f}%")
        
        if self.cache:
            cache_total = self.stats['cache_hits'] + self.stats['cache_misses']
            if cache_total > 0:
                cache_hit_rate = self.stats['cache_hits'] / cache_total * 100
                self.log_info(f"  缓存命中率: {cache_hit_rate:.1f}%")
        
        if self.stats['total_time'] > 0:
            avg_time = self.stats['total_time'] / total_requests
            self.log_info(f"  平均响应时间: {avg_time:.3f}秒")
    
    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total_requests = self.stats['total_requests']
        runtime = time.time() - self.stats['start_time']
        
        report = {
            'total_requests': total_requests,
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'success_rate': (self.stats['successful_requests'] / total_requests * 100) if total_requests > 0 else 0,
            'requests_per_second': total_requests / runtime if runtime > 0 else 0,
            'total_runtime': runtime,
            'average_response_time': (self.stats['total_time'] / total_requests) if total_requests > 0 else 0
        }
        
        if self.cache:
            cache_total = self.stats['cache_hits'] + self.stats['cache_misses']
            report.update({
                'cache_hits': self.stats['cache_hits'],
                'cache_misses': self.stats['cache_misses'],
                'cache_hit_rate': (self.stats['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
            })
        
        return report
    
    def clear_cache(self):
        """清空缓存"""
        if self.cache:
            self.cache.clear()
            self.log_info("缓存已清空")
    
    def reset_stats(self):
        """重置统计"""
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_time': 0.0,
            'start_time': time.time()
        }
        self.log_info("性能统计已重置")
